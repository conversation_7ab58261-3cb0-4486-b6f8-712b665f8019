{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  // Since this is a breaking change, this is temporarily flagged\n  // and will be false by default.\n  // In the next major (v16), this will be automatically enabled\n  if (process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL) {\n    if (!hasDataAttribute) {\n      // No smooth scrolling configured, run directly without style manipulation\n      fn()\n      return\n    }\n  } else {\n    // Old behavior: always manipulate styles, but warn about upcoming change\n\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !hasDataAttribute &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. In a future version, ' +\n          'Next.js will no longer automatically disable smooth scrolling during route transitions. ' +\n          'To prepare for this change, add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "__NEXT_OPTIMIZE_ROUTER_SCROLL", "NODE_ENV", "getComputedStyle", "warnOnce", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": "AAuBMU,QAAQC,GAAG,CAACC,6BAA6B,EAAE;;;;;+BAjBjCZ,4CAAAA;;;eAAAA;;;0BANS;AAMlB,SAASA,yCACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,+DAA+D;IAC/D,gCAAgC;IAChC,8DAA8D;IAC9D;;SAMO;QACL,yEAAyE;QAEzE,wEAAwE;QACxE,IACEC,QAAQC,GAAG,CAACE,QAAQ,gCAAK,iBACzB,CAACN,oBACDO,iBAAiBV,aAAaK,cAAc,KAAK,UACjD;YACAM,CAAAA,GAAAA,UAAAA,QAAQ,EACN,sFACE,6FACA,6FACA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,WAAWZ,YAAYa,KAAK,CAACR,cAAc;IACjDL,YAAYa,KAAK,CAACR,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQgB,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFd,YAAYe,cAAc;IAC5B;IACAlB;IACAG,YAAYa,KAAK,CAACR,cAAc,GAAGO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_ROUTER_BF_CACHE ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useRouterBFCache", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_ROUTER_BF_CACHE", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "useState", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": "AAI6BE,QAAQC,GAAG,CAACC,sBAAsB;;;;;+BA6B/CJ,oBAAAA;;;eAAAA;;;uBAhCS;AAEzB,kEAAkE;AAClE,MAAMC,6DAA4D,0BAAI;AA6B/D,SAASD,iBACdK,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,GAAGC,CAAAA,GAAAA,OAAAA,QAAQ,EACpD;QACE,MAAMC,eAAmC;YACvCC,MAAMN;YACNO,UAAUN;YACVO,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIH,gBAAgBI,IAAI,KAAKN,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMO,iBAAqC;QACzCH,MAAMN;QACNO,UAAUN;QACVO,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCT;IAC1C,IAAIU,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKN,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DW,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAL,mBAAmBM;IACnB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON><PERSON>,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { useRouterBFCache, type RouterBFCacheEntry } from './bfcache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nconst Activity = process.env.__NEXT_ROUTER_BF_CACHE\n  ? (require('react') as typeof import('react')).unstable_Activity\n  : null!\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      disableSmoothScrollDuringRouteTransition(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\nfunction RenderChildren({ children }: { children: React.ReactNode }) {\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n  gracefullyDegrade,\n  segmentViewBoundaries,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n  gracefullyDegrade?: boolean\n  segmentViewBoundaries?: React.ReactNode\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n  const parentTreeSegment = parentTree[0]\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const activeTree = parentTree[1][parallelRouterKey]\n  const activeSegment = activeTree[0]\n  const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n\n  // At each level of the route tree, not only do we render the currently\n  // active segment — we also render the last N segments that were active at\n  // this level inside a hidden <Activity> boundary, to preserve their state\n  // if or when the user navigates to them again.\n  //\n  // bfcacheEntry is a linked list of FlightRouterStates.\n  let bfcacheEntry: RouterBFCacheEntry | null = useRouterBFCache(\n    activeTree,\n    activeStateKey\n  )\n  let children: Array<React.ReactNode> = []\n  do {\n    const tree = bfcacheEntry.tree\n    const stateKey = bfcacheEntry.stateKey\n    const segment = tree[0]\n    const cacheKey = createRouterCacheKey(segment)\n\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey)\n    if (cacheNode === undefined) {\n      // When data is not available during rendering client-side we need to fetch\n      // it from the server.\n      const newLazyCacheNode: LazyCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n\n      // Flight data fetch kicked off during render and put into the cache.\n      cacheNode = newLazyCacheNode\n      segmentMap.set(cacheKey, newLazyCacheNode)\n    }\n\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n    const ErrorBoundaryComponent = gracefullyDegrade\n      ? RenderChildren\n      : ErrorBoundary\n\n    let segmentBoundaryTriggerNode: React.ReactNode = null\n    let segmentViewStateNode: React.ReactNode = null\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      process.env.__NEXT_DEVTOOL_SEGMENT_EXPLORER\n    ) {\n      const { SegmentBoundaryTriggerNode, SegmentViewStateNode } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      const pagePrefix = normalizeAppPath(url)\n      segmentViewStateNode = (\n        <SegmentViewStateNode key={pagePrefix} page={pagePrefix} />\n      )\n\n      segmentBoundaryTriggerNode = (\n        <>\n          <SegmentBoundaryTriggerNode />\n        </>\n      )\n    }\n\n    // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading\n    let child = (\n      <TemplateContext.Provider\n        key={stateKey}\n        value={\n          <ScrollAndFocusHandler segmentPath={segmentPath}>\n            <ErrorBoundaryComponent\n              errorComponent={error}\n              errorStyles={errorStyles}\n              errorScripts={errorScripts}\n            >\n              <LoadingBoundary loading={loadingModuleData}>\n                <HTTPAccessFallbackBoundary\n                  notFound={notFound}\n                  forbidden={forbidden}\n                  unauthorized={unauthorized}\n                >\n                  <RedirectBoundary>\n                    <InnerLayoutRouter\n                      url={url}\n                      tree={tree}\n                      cacheNode={cacheNode}\n                      segmentPath={segmentPath}\n                    />\n                    {segmentBoundaryTriggerNode}\n                  </RedirectBoundary>\n                </HTTPAccessFallbackBoundary>\n              </LoadingBoundary>\n            </ErrorBoundaryComponent>\n            {segmentViewStateNode}\n          </ScrollAndFocusHandler>\n        }\n      >\n        {templateStyles}\n        {templateScripts}\n        {template}\n      </TemplateContext.Provider>\n    )\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentStateProvider } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      child = (\n        <SegmentStateProvider key={stateKey}>\n          {child}\n          {segmentViewBoundaries}\n        </SegmentStateProvider>\n      )\n    }\n\n    if (process.env.__NEXT_ROUTER_BF_CACHE) {\n      child = (\n        <Activity\n          key={stateKey}\n          mode={stateKey === activeStateKey ? 'visible' : 'hidden'}\n        >\n          {child}\n        </Activity>\n      )\n    }\n\n    children.push(child)\n\n    bfcacheEntry = bfcacheEntry.next\n  } while (bfcacheEntry !== null)\n\n  return children\n}\n"], "names": ["OuterLayoutRouter", "Activity", "process", "env", "__NEXT_ROUTER_BF_CACHE", "require", "unstable_Activity", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "disableSmoothScrollDuringRouteTransition", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_PATCH", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "RenderChildren", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "gracefully<PERSON><PERSON><PERSON>", "segmentViewBoundaries", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "concat", "activeTree", "activeSegment", "activeStateKey", "createRouterCache<PERSON>ey", "bfcacheEntry", "useRouterBFCache", "stateKey", "cache<PERSON>ey", "newLazyCacheNode", "head", "prefetchHead", "ErrorBoundaryComponent", "Error<PERSON>ou<PERSON><PERSON>", "segmentBoundaryTriggerNode", "segmentViewStateNode", "__NEXT_DEVTOOL_SEGMENT_EXPLORER", "SegmentBoundaryTriggerNode", "SegmentViewStateNode", "pagePrefix", "normalizeAppPath", "page", "child", "TemplateContext", "errorComponent", "HTTPAccessFallbackBoundary", "RedirectBoundary", "SegmentStateProvider", "mode", "push", "next"], "mappings": "AA4CiBE,QAAQC,GAAG,CAACC,sBAAsB;AA5CnD;;;;;+BA8eA;;;CAGC,GACD,WAAA;;;eAAwBJ;;;;;;oCAnejB;iEASA;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;qCAC4B;kCACxB;gCACU;sCACN;mDACa;gCACV;yBACkB;0BACzB;AAEjC,MAAMC,iDACDI,QAAQ,SAAoCC,SAC7C,QAD8D;AAGlE;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,CAAAA,GAAAA,eAAAA,YAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMU,+DACJC,UAAAA,OAAQ,CACRD,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASE,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJL,6DAA6DE,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,IAAI5B,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAe;YAC1CC,QAAQC,IAAI,CACV,4FACAN;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMO,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOV,eAAeW,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBX,OAAoB,EAAEY,cAAsB;IAC1E,MAAML,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,aAAAA,KAAAA,OAAxBC,2BACA,AACAA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE,mDADmD;AAGlG;AAMA,MAAMK,mCAAmCC,OAAAA,OAAK,CAACC,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC9C,MAAM,KAAK,KAC1C,CAACyC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC1B,SAASoD,QAC1BhD,CAAAA,GAAAA,eAAAA,YAAY,EAACJ,SAASmD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU1C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE0C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMvC,kBAAkBqC,SAAU;oBACtE,IAAI7D,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAc;4BACrCgC;wBAAJ,IAAIA,CAAAA,CAAAA,yBAAAA,QAAQG,aAAa,KAAA,OAAA,KAAA,IAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCU,CAAAA,GAAAA,qBAAAA,wCAAwC,EACtC;oBACE,uEAAuE;oBACvE,IAAI3B,cAAc;;wBACdqB,QAAwBO,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc5B,SAAS6B,eAAe;oBAC5C,MAAMjC,iBAAiBgC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAInC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HgC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACpC,uBAAuByB,SAAwBxB,iBAAiB;wBACnE,0EAA0E;;wBACxEwB,QAAwBO,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBtB,kBAAkBsB,cAAc;gBAClD;gBAGF,8FAA8F;gBAC9FtB,kBAAkBsB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3Bb,QAAQc,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BpB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMsB,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACnC,4BAAAA;QACCW,aAAaA;QACbJ,mBAAmByB,QAAQzB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS0B,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJ1B,WAAW,EACX2B,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAMP,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMG,QAAQ,EAAE,GAAGR;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMS,sBACJH,UAAUI,WAAW,KAAK,OAAOJ,UAAUI,WAAW,GAAGJ,UAAUK,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,MAAWC,CAAAA,GAAAA,OAAAA,gBAAgB,EAACN,UAAUK,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMI,cACJ,OAAOF,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIG,IAAI,KAAK,aAC3DC,CAAAA,GAAAA,OAAAA,GAAG,EAACJ,OACJA;IAEN,IAAI,CAACE,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIG,WAAWV,UAAUU,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAczF,eAAe;gBAAC;mBAAOmD;aAAY,EAAE6B;YACzD,MAAMU,iBAAiBC,CAAAA,GAAAA,mCAAAA,iCAAiC,EAACX;YACzD,MAAMY,cAAcC,KAAKC,GAAG;YAC5BhB,UAAUU,QAAQ,GAAGA,WAAWO,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjD,IAAIC,IAAIjB,KAAKkB,SAASC,MAAM,GAC5B;gBACEC,mBAAmBV;gBACnBW,SAASV,iBAAiBlB,QAAQ4B,OAAO,GAAG;YAC9C,GACAd,IAAI,CAAC,CAACe;gBACNC,CAAAA,GAAAA,OAAAA,eAAe,EAAC;oBACdC,CAAAA,GAAAA,gBAAAA,uBAAuB,EAAC;wBACtBC,MAAMC,oBAAAA,mBAAmB;wBACzBC,cAAc1B;wBACdqB;wBACAT;oBACF;gBACF;gBAEA,OAAOS;YACT;YAEA,gDAAgD;YAChDd,CAAAA,GAAAA,OAAAA,GAAG,EAACC;QACN;QACA,yGAAyG;QACzG,iIAAiI;QACjID,CAAAA,GAAAA,OAAAA,GAAG,EAACoB,oBAAAA,kBAAkB;IACxB;IAEA,yEAAyE;IACzE,MAAMC,UACJ,cACA,CAAA,GAAA,YAAA,GAAA,EAACC,+BAAAA,UAD2E,SACxD,CAACC,QAAQ,EAAA;QAC3BC,OAAO;YACLC,YAAYnC;YACZoC,iBAAiBnC;YACjBoC,mBAAmB/D;YAEnB,kDAAkD;YAClD4B,KAAKA;QACP;kBAECM;;IAGL,iFAAiF;IACjF,OAAOuB;AACT;AAEA;;;CAGC,GACD,SAASO,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACPlE,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAImE;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgB9B,IAAI,KAAK,YACjC;QACA,MAAMgC,oBAAoBF;QAC1BC,oBAAoB9B,CAAAA,GAAAA,OAAAA,GAAG,EAAC+B;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACK,OAAAA,QAAQ,EAAA;YACPC,UAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;oBACGH;oBACAC;oBACAF;;;sBAIJrE;;IAGP;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAEA,SAAS0E,eAAe,KAA2C;IAA3C,IAAA,EAAE1E,QAAQ,EAAiC,GAA3C;IACtB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAMe,SAASzD,kBAAkB,KA0BzC;IA1ByC,IAAA,EACxCoI,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,iBAAiB,EACjBC,qBAAqB,EActB,GA1ByC;IA2BxC,MAAMhE,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACoC,+BAAAA,mBAAmB;IAC9C,IAAI,CAACrC,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIG,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAEqC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAEnC,GAAG,EAAE,GAAGP;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAMiE,uBAAuBxB,gBAAgByB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACf;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACc,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAACjB,mBAAmBc;IAC9C;IACA,MAAMI,oBAAoB/B,UAAU,CAAC,EAAE;IACvC,MAAM7D,cACJ+D,sBAAsB,OAElB,AACA,qCAAqC,iCADiC;IAEtE;QAACW;KAAkB,GACnBX,kBAAkB8B,MAAM,CAAC;QAACD;QAAmBlB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMoB,aAAajC,UAAU,CAAC,EAAE,CAACa,kBAAkB;IACnD,MAAMqB,gBAAgBD,UAAU,CAAC,EAAE;IACnC,MAAME,iBAAiBC,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACF,eAAe,MAAM,mBAAmB;;IAEpF,uEAAuE;IACvE,0EAA0E;IAC1E,0EAA0E;IAC1E,+CAA+C;IAC/C,EAAE;IACF,uDAAuD;IACvD,IAAIG,eAA0CC,CAAAA,GAAAA,SAAAA,gBAAgB,EAC5DL,YACAE;IAEF,IAAIjG,WAAmC,EAAE;IACzC,GAAG;QACD,MAAM2B,OAAOwE,aAAaxE,IAAI;QAC9B,MAAM0E,WAAWF,aAAaE,QAAQ;QACtC,MAAMpJ,UAAU0E,IAAI,CAAC,EAAE;QACvB,MAAM2E,WAAWJ,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACjJ;QAEtC,yDAAyD;QACzD,IAAI2E,YAAY6D,WAAWC,GAAG,CAACY;QAC/B,IAAI1E,cAAcpE,WAAW;YAC3B,2EAA2E;YAC3E,sBAAsB;YACtB,MAAM+I,mBAAkC;gBACtCjE,UAAU;gBACVL,KAAK;gBACLD,aAAa;gBACbwE,MAAM;gBACNC,cAAc;gBACdjB,gBAAgB,IAAIG;gBACpBzB,SAAS;gBACTxB,aAAa,CAAC;YAChB;YAEA,qEAAqE;YACrEd,YAAY2E;YACZd,WAAWG,GAAG,CAACU,UAAUC;QAC3B;QAEA;;;;;;;;;EASF,GAEE,MAAMG,yBAAyBrB,oBAC3BX,iBACAiC,eAAAA,aAAa;QAEjB,IAAIC,6BAA8C;QAClD,IAAIC,uBAAwC;QAC5C,IACEpK,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,gBACzB7B,QAAQC,GAAG,CAACoK,+BAA+B,EAC3C;;QAgBF,4EAA4E;QAC5E,wEAAwE;QACxE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,8EAA8E;QAC9E,6DAA6D;QAC7D,MAAM3C,oBAAoBJ,gBAAgBG,OAAO;QACjD,IAAIkD,QAAAA,WAAAA,GACF,CAAA,GAAA,YAAA,IAAA,EAACC,+BAAAA,eAAe,CAACzD,QAAQ,EAAA;YAEvBC,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACxC,uBAAAA;gBAAsBpB,aAAaA;;kCAClC,CAAA,GAAA,YAAA,GAAA,EAACyG,wBAAAA;wBACCY,gBAAgB1C;wBAChBC,aAAaA;wBACbC,cAAcA;kCAEd,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACb,iBAAAA;4BAAgBC,SAASC;sCACxB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACoD,gBAAAA,0BAA0B,EAAA;gCACzBrC,UAAUA;gCACVC,WAAWA;gCACXC,cAAcA;0CAEd,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAACoC,kBAAAA,gBAAgB,EAAA;;sDACf,CAAA,GAAA,YAAA,GAAA,EAAC9F,mBAAAA;4CACCG,KAAKA;4CACLF,MAAMA;4CACNC,WAAWA;4CACX3B,aAAaA;;wCAEd2G;;;;;;oBAKRC;;;;gBAIJ9B;gBACAC;gBACAC;;WAhCIoB;QAoCT,IAAI5J,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAEmJ,oBAAoB,EAAE,GAC5B7K,QAAQ;YAEVwK,QAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACK,sBAAAA;;oBACEL;oBACA9B;;eAFwBe;QAK/B;QAEA,IAAI5J,QAAQC,GAAG,CAACC,sBAAsB,EAAE;;QAWxCqD,SAAS2H,IAAI,CAACP;QAEdjB,eAAeA,aAAayB,IAAI;IAClC,QAASzB,iBAAiB,KAAK;IAE/B,OAAOnG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["RenderFromTemplateContext", "children", "useContext", "TemplateContext"], "mappings": ";;;+BAKA,WAAA;;;eAAwBA;;;;;iEAHoB;+CACZ;AAEjB,SAASA;IACtB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,eAAe;IAC3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGF;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n  '_debugInfo',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["describeHasCheckingStringProperty", "describeStringPropertyAccess", "wellKnownProperties", "isDefinitelyAValidIdentifier", "target", "prop", "test", "JSON", "stringify", "stringifiedProp", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;;;;;;;;IAU3CA,iCAAiC,EAAA;eAAjCA;;IAPAC,4BAA4B,EAAA;eAA5BA;;IAeHC,mBAAmB,EAAA;eAAnBA;;;AAjBb,MAAMC,+BAA+B;AAE9B,SAASF,6BAA6BG,MAAc,EAAEC,IAAY;IACvE,IAAIF,6BAA6BG,IAAI,CAACD,OAAO;QAC3C,OAAQ,MAAID,SAAO,MAAGC,OAAK;IAC7B;IACA,OAAQ,MAAID,SAAO,MAAGG,KAAKC,SAAS,CAACH,QAAM;AAC7C;AAEO,SAASL,kCACdI,MAAc,EACdC,IAAY;IAEZ,MAAMI,kBAAkBF,KAAKC,SAAS,CAACH;IACvC,OAAQ,kBAAgBD,SAAO,OAAIK,kBAAgB,UAASA,kBAAgB,SAAML,SAAO;AAC3F;AAEO,MAAMF,sBAAsB,IAAIQ,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB,cAAc;IACd;IACA;IACA;IAEA,0BAA0B;IAC1B,cAAc;IACd;IAEA,sBAAsB;IACtB;IACA;IAEA,2BAA2B;IAC3B,cAAc;IACd;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nfunction makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeUntrackedExoticSearchParamsWithDevWarnings`, but just logging\n// the sync access without actually defining the search params on the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nexport function createRenderSearchParamsFromClient(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  if (process.env.__NEXT_DYNAMIC_IO) {\n    return makeUntrackedSearchParamsWithDevWarnings(underlyingSearchParams)\n  }\n\n  return makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams)\n}\n"], "names": ["createRenderSearchParamsFromClient", "CachedSearchParams", "WeakMap", "makeUntrackedExoticSearchParamsWithDevWarnings", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "describeHasCheckingStringProperty", "ownKeys", "warnForSyncSpread", "makeUntrackedSearchParamsWithDevWarnings", "console", "error", "process", "env", "__NEXT_DYNAMIC_IO"], "mappings": "AAmLMuC,QAAQC,GAAG,CAACC,iBAAiB,EAAE;;;;;+BAHrBzC,sCAAAA;;;eAAAA;;;yBA9Ke;8BAKxB;AAGP,MAAMC,qBAAqB,IAAIC;AAE/B,SAASC,+CACPC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEAtB,mBAAmB6B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,gFAAgF;AAChF,8EAA8E;AAC9E,SAASe,yCACPhC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;QACxB;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEAtB,mBAAmB6B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,SAASO,kBAAkBF,UAAkB;IAC3CW,QAAQC,KAAK,CACV,uDAAoDZ,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASS;IACPE,QAAQC,KAAK,CACV,wDACE,4FACA;AAEP;AAEO,SAAStC,mCACdI,sBAAoC;IAEpC;;IAIA,OAAOD,+CAA+CC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).createRenderSearchParamsFromClient\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).createRenderSearchParamsFromClient\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,sCAAAA;;;eAAAA;;;AAAN,MAAMA,4EAGLI,QAAQ,0HACRJ,kCAAkC,GAElCI,QAAQ,gCACRJ,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`\n  )\n}\n\nfunction warnForEnumeration(missingProperties: Array<string>) {\n  if (missingProperties.length) {\n    const describedMissingProperties =\n      describeListOfPropertyNames(missingProperties)\n    console.error(\n      `params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  } else {\n    console.error(\n      `params are being enumerated. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  }\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n\nexport function createRenderParamsFromClient(\n  clientParams: Params\n): Promise<Params> {\n  if (process.env.__NEXT_DYNAMIC_IO) {\n    return makeDynamicallyTrackedParamsWithDevWarnings(clientParams)\n  }\n\n  return makeDynamicallyTrackedExoticParamsWithDevWarnings(clientParams)\n}\n"], "names": ["createRenderParamsFromClient", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "unproxiedProperties", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "makeDynamicallyTrackedParamsWithDevWarnings", "console", "error", "missingProperties", "length", "describedMissingProperties", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i", "clientParams", "process", "env", "__NEXT_DYNAMIC_IO"], "mappings": "AA2KM8C,QAAQC,GAAG,CAACC,iBAAiB,EAAE;;;;;+BAHrBhD,gCAAAA;;;eAAAA;;;yBAtKe;gCACA;8BAIxB;AAGP,MAAMC,eAAe,IAAIC;AAEzB,SAASC,kDACPC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBS,GAAG,CAACH;YACpBT,OAAe,CAACS,KAAK,GAAGZ,gBAAgB,CAACY,KAAK;QAClD;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMd,SAAS;QACxCD,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBQ,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BN,kBAAkBoB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU,mBAAmBpB;YACnB,OAAOqB,QAAQF,OAAO,CAACT;QACzB;IACF;IAEArB,aAAa2B,GAAG,CAACxB,kBAAkBgB;IACnC,OAAOA;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,SAASc,4CACP9B,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBS,GAAG,CAACH;QACxB;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMd,SAAS;QACxCD,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBQ,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BN,kBAAkBoB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU,mBAAmBpB;YACnB,OAAOqB,QAAQF,OAAO,CAACT;QACzB;IACF;IAEArB,aAAa2B,GAAG,CAACxB,kBAAkBgB;IACnC,OAAOA;AACT;AAEA,SAASM,kBAAkBF,UAAkB;IAC3CW,QAAQC,KAAK,CACV,iDAA8CZ,aAAW;AAE9D;AAEA,SAASQ,mBAAmBK,iBAAgC;IAC1D,IAAIA,kBAAkBC,MAAM,EAAE;QAC5B,MAAMC,6BACJC,4BAA4BH;QAC9BF,QAAQC,KAAK,CACV,wEAAqEG,6BAA2B,OAC9F,6EACA;IAEP,OAAO;QACLJ,QAAQC,KAAK,CACV,kCACE,6EACA;IAEP;AACF;AAEA,SAASI,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWH,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAII,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAQ,MAAID,UAAU,CAAC,EAAE,GAAC;QAC5B,KAAK;YACH,OAAQ,MAAIA,UAAU,CAAC,EAAE,GAAC,YAAWA,UAAU,CAAC,EAAE,GAAC;QACrD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWH,MAAM,GAAG,GAAGM,IAAK;oBAC9CD,eAAgB,MAAIF,UAAU,CAACG,EAAE,GAAC;gBACpC;gBACAD,eAAgB,YAAUF,UAAU,CAACA,WAAWH,MAAM,GAAG,EAAE,GAAC;gBAC5D,OAAOK;YACT;IACF;AACF;AAEO,SAAS3C,6BACd6C,YAAoB;IAEpB;;IAIA,OAAO1C,kDAAkD0C;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .createRenderParamsFromClient\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).createRenderParamsFromClient\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,gCAAAA;;;eAAAA;;;AAAN,MAAMA,sEAENI,QAAQ,mHACNJ,4BAA4B,GAE7BI,QAAQ,yBACRJ,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": "AAauBO,QAAQC,GAAG,CAACC,iBAAiB;;;;;+BA4BpCT,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,uDACFI,QAAQC,KAAK,aACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC;;YAAmBC,uBAAH,KAAa;;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;IAGT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/app-render/after-task-async-storage-instance.ts"], "sourcesContent": ["import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["afterTaskAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,iCAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,gCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/app-render/after-task-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n"], "names": ["afterTaskAsyncStorage"], "mappings": ";;;+BAiBSA,yBAAAA;;;eAAAA,+BAAAA,6BAAqB;;;+CAdyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore,\n  constructorOpt: Function\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  Error.captureStackTrace(error, constructorOpt)\n  workStore.invalidDynamicUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "constructorOpt", "error", "Error", "captureStackTrace", "invalidDynamicUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;;IAoCgBA,+BAA+B,EAAA;eAA/BA;;IAdAC,oCAAoC,EAAA;eAApCA;;IAlBAC,qCAAqC,EAAA;eAArCA;;IASAC,qDAAqD,EAAA;eAArDA;;;yCAbsB;+CACA;AAG/B,SAASD,sCACdE,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,AAAC,MAAM,UAAEF,OAAM,iDAAiD,WAAEC,YAAW,0HAA0H,CAAC,KADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASF,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,AAAC,MAAM,UAAEF,OAAM,4EAA4E,UAAEC,YAAW,0HAA0H,CAAC,KAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASJ,qCACdM,SAAoB,EACpBC,cAAwB;QAOxBD;IALA,MAAME,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,AAAC,MAAM,UAAEH,UAAUH,KAAK,EAAC,oVAAoV,CAAC,KADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAM,MAAMC,iBAAiB,CAACF,OAAOD;;8KACrBI,wBAAwB,GAAKH;IAEvC,MAAMA;AACR;AAEO,SAAST;IACd,MAAMa,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (\n    prerenderStore &&\n    (prerenderStore.type === 'prerender' ||\n      prerenderStore.type === 'prerender-client')\n  ) {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a dynamicIO (PPR or otherwise) prerender\n      return makeHangingSearchParams(prerenderStore)\n    default:\n      // The remaining cases are prerender-ppr and prerender-legacy\n      // We are in a legacy static generation and need to interrupt the prerender\n      // when search params are accessed.\n      return makeErroringExoticSearchParams(workStore, prerenderStore)\n  }\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      if (process.env.__NEXT_DYNAMIC_IO) {\n        return makeUntrackedSearchParamsWithDevWarnings(\n          underlyingSearchParams,\n          workStore\n        )\n      }\n\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      if (process.env.__NEXT_DYNAMIC_IO) {\n        return makeUntrackedSearchParams(underlyingSearchParams)\n      }\n\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has: function has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, has)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys: function ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore, ownKeys)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticSearchParamsWithDevWarnings`, but\n// just logging the sync access without actually defining the search params on\n// the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createPrerenderSearchParamsForClientPage", "createSearchParamsFromClient", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "makeErroringExoticSearchParamsForUseCache", "underlyingSearchParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderSearchParams", "createRenderSearchParams", "forceStatic", "Promise", "resolve", "prerenderStore", "makeHangingPromise", "renderSignal", "makeHangingSearchParams", "makeErroringExoticSearchParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeUntrackedSearchParamsWithDevWarnings", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "makeUntrackedSearchParams", "makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "ReflectAdapter", "expression", "annotateDynamicAccess", "set", "dynamicShouldError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "wellKnownProperties", "has", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "ownKeys", "throwForSearchParamsAccessInUseCache", "store", "keys", "for<PERSON>ach", "defineProperty", "trackDynamicDataInDynamicRender", "value", "writable", "enumerable", "configurable", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "scheduleImmediate", "then", "push", "add", "newValue", "syncIODev", "delete", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "length", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createSearchAccessError", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AAmKMqB,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IA3DVvB,wCAAwC,EAAA;eAAxCA;;IA1CAC,4BAA4B,EAAA;eAA5BA;;IAoBHC,mCAAmC,EAAA;eAAnCA;;IAGGC,qCAAqC,EAAA;eAArCA;;IAwUAC,yCAAyC,EAAA;eAAzCA;;;yBA3Ze;kCAOxB;8CAQA;gCACwB;uCACI;0DACyB;8BAKrD;uBAIA;2BAC2B;AAgC3B,SAASH,6BACdI,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAGO,MAAMJ,sCACXC;AAEK,SAASA,sCACdE,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAEO,SAASN,yCACdM,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMC,iBAAiBR,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IACEO,kBACCA,CAAAA,eAAeN,IAAI,KAAK,eACvBM,eAAeN,IAAI,KAAK,kBAAiB,GAC3C;QACA,sBAAsB;QACtB,wEAAwE;QACxE,2DAA2D;QAC3D,OAAOO,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACD,eAAeE,YAAY,EAAE;IACzD;IACA,oFAAoF;IACpF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOJ,QAAQC,OAAO,CAAC,CAAC;AAC1B;AAEA,SAASJ,4BACPL,SAAoB,EACpBU,cAA8B;IAE9B,IAAIV,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQC,eAAeN,IAAI;QACzB,KAAK;QACL,KAAK;YACH,qDAAqD;YACrD,OAAOS,wBAAwBH;QACjC;YACE,6DAA6D;YAC7D,2EAA2E;YAC3E,mCAAmC;YACnC,OAAOI,+BAA+Bd,WAAWU;IACrD;AACF;AAEA,SAASJ,yBACPP,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,wDAC2B,iBACzB,CAACT,UAAUkB,iBAAiB,EAC5B;YACA,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;YAOnC,OAAOE,wDACLtB,wBACAC;QAEJ,OAAO;YACL,IAAIe,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;YAInC,OAAOI,gCAAgCxB,wBAAwBC;QACjE;IACF;AACF;AAGA,MAAMwB,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASZ,wBACPH,cAAoC;IAEpC,MAAMiB,qBAAqBH,mBAAmBI,GAAG,CAAClB;IAClD,IAAIiB,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCD,eAAeE,YAAY,EAC3B;IAGF,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY5B;wBAClC,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMI,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY5B;wBAClC,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOG,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBgB,GAAG,CAAC9B,gBAAgBoB;IACvC,OAAOA;AACT;AAEA,SAAShB,+BACPd,SAAoB,EACpBU,cAAwD;IAExD,MAAMiB,qBAAqBH,mBAAmBI,GAAG,CAAC5B;IAClD,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM5B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAM8B,UAAUrB,QAAQC,OAAO,CAACV;IAEhC,MAAM+B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACF,IAAItC,UAAUyC,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;wBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAM4B,aACJ;wBACF,IAAItC,UAAUyC,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;wBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOuB,SAAS,YAAY,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;4BAC9D,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAC7C,gBACAhB;4BAEF,IAAIjC,UAAUyC,kBAAkB,EAAE;gCAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;4BAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;gCAClD,+BAA+B;gCAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;4BAElC,OAAO;gCACL,mBAAmB;gCACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;4BAEJ;wBACF;wBACA,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;gBAEF,IAAIjC,UAAUyC,kBAAkB,EAAE;oBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;gBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;oBAClD,+BAA+B;oBAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;gBAElC,OAAO;oBACL,mBAAmB;oBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;gBAEJ;gBACA,OAAO;YACT;YACA,OAAO2B,SAAAA,cAAc,CAACW,GAAG,CAAChB,QAAQC;QACpC;QACAkB;YACE,MAAMb,aACJ;YACF,IAAItC,UAAUyC,kBAAkB,EAAE;gBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;YAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;gBAClD,+BAA+B;gBAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;YAElC,OAAO;gBACL,mBAAmB;gBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAACR,YAAYtC,WAAWU;YAC1D;QACF;IACF;IAEAc,mBAAmBgB,GAAG,CAACxC,WAAW8B;IAClC,OAAOA;AACT;AAOO,SAAShC,0CACdE,SAAoB;IAEpB,MAAM2B,qBAAqBD,8BAA8BE,GAAG,CAAC5B;IAC7D,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUrB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMqB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,KAAI,GACjD;gBACAmB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAW4B;YAClD;YAEA,OAAOS,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAc,KAAK,SAASA,IAAIhB,MAAM,EAAEC,IAAI;YAC5B,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,KAAI,GACjD;gBACAmB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAWgD;YAClD;YAEA,OAAOX,SAAAA,cAAc,CAACW,GAAG,CAAChB,QAAQC;QACpC;QACAkB,SAAS,SAASA;YAChBC,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAWmD;QAClD;IACF;IAEAzB,8BAA8Bc,GAAG,CAACxC,WAAW8B;IAC7C,OAAOA;AACT;AAEA,SAASP,gCACPxB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUrB,QAAQC,OAAO,CAACV;IAChCyB,mBAAmBgB,GAAG,CAACzC,wBAAwB8B;IAE/CM,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAI,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YAClCE,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;gBACnCL;oBACE,MAAM3B,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;oBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;oBACvC,OAAOF,sBAAsB,CAACkC,KAAK;gBACrC;gBACAO,KAAIkB,KAAK;oBACPvB,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;wBACnCyB;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOhC;AACT;AAEA,SAASP,0BACPvB,sBAAoC;IAEpC,MAAM4B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUrB,QAAQC,OAAO,CAACV;IAChCyB,mBAAmBgB,GAAG,CAACzC,wBAAwB8B;IAE/C,OAAOA;AACT;AAEA,SAASR,wDACPtB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMmC,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAInC,MAAMhC,wBAAwB;QAC1D6B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYgC,oBAAoB;gBAClD,IAAIZ,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMH,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChES,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;gBAEJ;gBACA,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;gBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;YACzC;YACA,OAAOoC,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIoB,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMH,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFS,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;gBAEJ;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,IAAIqB,MAAMZ,kBAAkB,EAAE;gBAC5B,MAAMH,aACJ;gBACFI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;YAEJ;YACA,OAAO6B,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,UAAU,IAAIrB,QAAsB,CAACC,UACzC2D,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAM3D,QAAQV;IAElC8B,QAAQwC,IAAI,CAAC;QACXJ,qBAAqB;IACvB;IAEA9B,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAIc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE+B,oBAAoBM,IAAI,CAACrC;QAC3B,OAAO;YACL6B,kBAAkBS,GAAG,CAACtC;YACtBE,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;gBACnCL;oBACE,OAAOsC,iBAAiB,CAACjC,KAAK;gBAChC;gBACAO,KAAIgC,QAAQ;oBACVrC,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;wBACnCyB,OAAOc;wBACPb,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAM/B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUoB,MAAMZ,kBAAkB,EAAE;gBAC/C,MAAMH,aAAa;gBACnBI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;YAEJ;YACA,IAAI,OAAOL,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChEwC,UAAUpB,MAAMV,KAAK,EAAEL;gBACzB;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAM,KAAIR,MAAM,EAAEC,IAAI,EAAEyB,KAAK,EAAExB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B6B,kBAAkBY,MAAM,CAACzC;YAC3B;YACA,OAAOkC,QAAQ3B,GAAG,CAACR,QAAQC,MAAMyB,OAAOxB;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFwC,UAAUpB,MAAMV,KAAK,EAAEL;gBACzB;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,MAAMM,aAAa;YACnBmC,UAAUpB,MAAMV,KAAK,EAAEL,YAAY0B;YACnC,OAAOG,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEAR,mBAAmBgB,GAAG,CAACzC,wBAAwB+B;IAC/C,OAAOA;AACT;AAEA,4EAA4E;AAC5E,8EAA8E;AAC9E,eAAe;AACf,SAASV,yCACPrB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMmC,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMnC,UAAUrB,QAAQC,OAAO,CAACV;IAEhCoC,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAIc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE+B,oBAAoBM,IAAI,CAACrC;QAC3B,OAAO;YACL6B,kBAAkBS,GAAG,CAACtC;QACxB;IACF;IAEA,MAAMH,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChE0C,kBAAkBtB,MAAMV,KAAK,EAAEL;gBACjC;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAM,KAAIR,MAAM,EAAEC,IAAI,EAAEyB,KAAK,EAAExB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B6B,kBAAkBY,MAAM,CAACzC;YAC3B;YACA,OAAOkC,QAAQ3B,GAAG,CAACR,QAAQC,MAAMyB,OAAOxB;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEF0C,kBAAkBtB,MAAMV,KAAK,EAAEL;gBACjC;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,MAAMM,aAAa;YACnBsC,6BAA6BvB,MAAMV,KAAK,EAAEL,YAAY0B;YACtD,OAAOG,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEAR,mBAAmBgB,GAAG,CAACzC,wBAAwB+B;IAC/C,OAAOA;AACT;AAEA,SAAS2C,UACP9B,KAAyB,EACzBL,UAAkB,EAClBuC,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDF,6BAA6BjC,OAAOL,YAAYuC;IAClD,OAAO;QACLF,kBAAkBhC,OAAOL;IAC3B;IAEA,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc8E,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe/E;QACrBgF,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;AACF;AAEA,MAAML,oBAAoBO,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,MAAMP,+BACJM,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACE;AAE9C,SAASD,wBACPxC,KAAyB,EACzBL,UAAkB;IAElB,MAAM+C,SAAS1C,QAAQ,AAAC,OAAO,GAAQ,EAAE,CAAC,IAATA,eAAY;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAI2C,MACT,UAAGD,QAAO,KAAK,WAAE/C,YAAW,EAAE,CAAC,GAC7B,EAAC,gEAAgE,CAAC,EACjE,CAAD,6DAA+D,CAAC,IAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS8C,iCACPzC,KAAyB,EACzBL,UAAkB,EAClBuC,iBAAgC;IAEhC,MAAMQ,SAAS1C,QAAS,AAAD,OAAQ,UAAEA,OAAM,EAAE,CAAC,KAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAI2C,MACT,UAAGD,QAAO,KAAK,WAAE/C,YAAW,EAAE,CAAC,GAC7B,EAAC,gEAAgE,CAAC,EACjE,CAAD,+DAAiE,CAAC,GAClE,CAAC,iEAAiE,CAAC,IACnE,UAAGiD,4BAA4BV,oBAAmB,EAAE,CAAC,GACrD,EAAC,8DAA8D,CAAC,IAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASU,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWV,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIW,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,AAAC,EAAE,SAAED,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAQ,AAAD,EAAG,SAAEA,UAAU,CAAC,EAAE,EAAC,SAAS,SAAEA,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWV,MAAM,GAAG,GAAGa,IAAK;oBAC9CD,eAAe,AAAC,EAAE,SAAEF,UAAU,CAACG,EAAE,EAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,AAAC,QAAQ,SAAEF,UAAU,CAACA,WAAWV,MAAM,GAAG,EAAE,EAAC,EAAE,CAAC;gBAC/D,OAAOY;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1984, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/app-render/dynamic-access-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { DynamicAccessStorage } from './dynamic-access-async-storage.external'\n\nexport const dynamicAccessAsyncStorageInstance: DynamicAccessStorage =\n  createAsyncLocalStorage()\n"], "names": ["dynamicAccessAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,qCAAAA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,oCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2001, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/app-render/dynamic-access-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { dynamicAccessAsyncStorageInstance } from './dynamic-access-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport interface DynamicAccessAsyncStore {\n  readonly abortController: AbortController\n}\n\nexport type DynamicAccessStorage = AsyncLocalStorage<DynamicAccessAsyncStore>\nexport { dynamicAccessAsyncStorageInstance as dynamicAccessAsyncStorage }\n"], "names": ["dynamicAccessAsyncStorage", "dynamicAccessAsyncStorageInstance"], "mappings": ";;;+BAU8CA,6BAAAA;;;eAArCC,mCAAAA,iCAAiC;;;mDAPQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/server/request/params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (\n    prerenderStore &&\n    (prerenderStore.type === 'prerender' ||\n      prerenderStore.type === 'prerender-client')\n  ) {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      switch (prerenderStore.type) {\n        case 'prerender':\n        case 'prerender-client':\n          // We are in a dynamicIO prerender\n          return makeHangingParams(underlyingParams, prerenderStore)\n        default:\n          return makeErroringExoticParams(\n            underlyingParams,\n            fallbackParams,\n            workStore,\n            prerenderStore\n          )\n      }\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeDynamicallyTrackedParamsWithDevWarnings(\n        underlyingParams,\n        workStore\n      )\n    }\n\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeUntrackedParams(underlyingParams)\n    }\n\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(prerenderStore.renderSignal, '`params`'),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderParams", "createRenderParams", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "Promise", "resolve", "hasSomeFallbackParams", "makeHangingParams", "makeErroringExoticParams", "makeUntrackedExoticParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeDynamicallyTrackedParamsWithDevWarnings", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedParams", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "ReflectAdapter", "args", "store", "dynamicAccessAsyncStorage", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "postponeWithTracking", "route", "dynamicTracking", "throwToInterruptStaticGeneration", "enumerable", "newValue", "value", "writable", "configurable", "scheduleImmediate", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "syncIODev", "delete", "ownKeys", "Reflect", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "length", "createDedupedByCallsiteServerErrorLoggerDev", "createParamsAccessError", "createIncompleteEnumerationError", "prefix", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AA6LM0B,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IAnIV5B,sBAAsB,EAAA;eAAtBA;;IA8DAC,qCAAqC,EAAA;eAArCA;;IAzCHC,6BAA6B,EAAA;eAA7BA;;IAGGC,0BAA0B,EAAA;eAA1BA;;IAmBAC,kCAAkC,EAAA;eAAlCA;;;yBAlGe;kCAKxB;8CAQA;gCACwB;8BAIxB;uCAC4B;0DACyB;2BAC1B;mDACQ;AAiCnC,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAIO,MAAMJ,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASF,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASL,sCACdI,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMO,iBAAiBL,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IACEI,kBACCA,CAAAA,eAAeH,IAAI,KAAK,eACvBG,eAAeH,IAAI,KAAK,kBAAiB,GAC3C;QACA,MAAMI,iBAAiBR,UAAUS,mBAAmB;QACpD,IAAID,gBAAgB;YAClB,IAAK,IAAIE,OAAOX,iBAAkB;gBAChC,IAAIS,eAAeG,GAAG,CAACD,MAAM;oBAC3B,4EAA4E;oBAC5E,+EAA+E;oBAC/E,kDAAkD;oBAClD,OAAOE,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACL,eAAeM,YAAY,EAAE;gBACzD;YACF;QACF;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOC,QAAQC,OAAO,CAAChB;AACzB;AAEA,SAASM,sBACPN,gBAAwB,EACxBC,SAAoB,EACpBO,cAA8B;IAE9B,MAAMC,iBAAiBR,UAAUS,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIQ,wBAAwB;QAC5B,IAAK,MAAMN,OAAOX,iBAAkB;YAClC,IAAIS,eAAeG,GAAG,CAACD,MAAM;gBAC3BM,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,OAAQT,eAAeH,IAAI;gBACzB,KAAK;gBACL,KAAK;oBACH,kCAAkC;oBAClC,OAAOa,kBAAkBlB,kBAAkBQ;gBAC7C;oBACE,OAAOW,yBACLnB,kBACAS,gBACAR,WACAO;YAEN;QACF;IACF;IAEA,qFAAqF;IACrF,OAAOY,0BAA0BpB;AACnC;AAEA,SAASO,mBACPP,gBAAwB,EACxBC,SAAoB;IAEpB,wDAA6B,iBAAiB,CAACA,UAAUuB,iBAAiB,EAAE;QAC1E,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;QAOnC,OAAOE,kDACL3B,kBACAC;IAEJ,OAAO;QACL,IAAIoB,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;QAInC,OAAOL,0BAA0BpB;IACnC;AACF;AAGA,MAAM6B,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBC,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC;;wBAAGI;;oBACV,MAAMC,QAAQC,mCAAAA,yBAAyB,CAACpC,QAAQ;oBAEhD,IAAImC,OAAO;wBACTA,MAAME,eAAe,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,IAAIC,MAAM,AAAC,iDAAiD,CAAC,EAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTR,eAAeS,KAAK,CAACZ,QAAQK,OAC7BP;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOG,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASjB,kBACPlB,gBAAwB,EACxBQ,cAAoC;IAEpC,MAAMsC,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,MAClB/B,CAAAA,GAAAA,uBAAAA,kBAAkB,EAASL,eAAeM,YAAY,EAAE,aACxDiB;IAGFF,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnC,OAAOA;AACT;AAEA,SAAS5B,yBACPnB,gBAAwB,EACxBS,cAAmC,EACnCR,SAAoB,EACpBO,cAAwD;IAExD,MAAMsC,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGjD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAM+C,UAAUhC,QAAQC,OAAO,CAACiC;IAChCpB,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnCG,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAIzB,eAAeG,GAAG,CAACsB,OAAO;gBAC5BgB,OAAOI,cAAc,CAACL,qBAAqBf,MAAM;oBAC/CF;wBACE,MAAMuB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI1B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/BoD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBxD,UAAUyD,KAAK,EACfH,YACA/C,eAAemD,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BL,YACAtD,WACAO;wBAEJ;oBACF;oBACAqD,YAAY;gBACd;gBACAX,OAAOI,cAAc,CAACP,SAASb,MAAM;oBACnCF;wBACE,MAAMuB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI1B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/BoD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBxD,UAAUyD,KAAK,EACfH,YACA/C,eAAemD,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BL,YACAtD,WACAO;wBAEJ;oBACF;oBACAwC,KAAIc,QAAQ;wBACVZ,OAAOI,cAAc,CAACP,SAASb,MAAM;4BACnC6B,OAAOD;4BACPE,UAAU;4BACVH,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZI,cAAc;gBAChB;YACF,OAAO;;gBACHlB,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;YAClD;QACF;IACF;IAEA,OAAOa;AACT;AAEA,SAAS3B,0BAA0BpB,gBAAwB;IACzD,MAAM8C,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUhC,QAAQC,OAAO,CAAChB;IAChC6B,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnCG,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHa,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;QAClD;IACF;IAEA,OAAOa;AACT;AAEA,SAASnB,oBAAoB5B,gBAAwB;IACnD,MAAM8C,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAUhC,QAAQC,OAAO,CAAChB;IAChC6B,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnC,OAAOA;AACT;AAEA,SAASpB,kDACP3B,gBAAwB,EACxBuC,KAAgB;IAEhB,MAAMO,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAU,IAAIhC,QAAgB,CAACC,UACnCkD,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMlD,QAAQhB;IAGlC,MAAMmE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CnB,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEmC,oBAAoBC,IAAI,CAACpC;QAC3B,OAAO;YACLiC,kBAAkBI,GAAG,CAACrC;YACpBa,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;QAClD;IACF;IAEA,MAAMsC,iBAAiB,IAAI5B,MAAMG,SAAS;QACxCf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAiC,kBAAkBvD,GAAG,CAACsB,OACtB,0CAFuE;oBAGvE,MAAMqB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;oBAC1DuC,UAAUlC,MAAMmB,KAAK,EAAEH;gBACzB;YACF;YACA,OAAOlB,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAE6B,KAAK,EAAE5B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BiC,kBAAkBO,MAAM,CAACxC;YAC3B;YACA,OAAOG,SAAAA,cAAc,CAACW,GAAG,CAACf,QAAQC,MAAM6B,OAAO5B;QACjD;QACAwC,SAAQ1C,MAAM;YACZ,MAAMsB,aAAa;YACnBkB,UAAUlC,MAAMmB,KAAK,EAAEH,YAAYc;YACnC,OAAOO,QAAQD,OAAO,CAAC1C;QACzB;IACF;IAEAJ,aAAamB,GAAG,CAAChD,kBAAkBwE;IACnC,OAAOA;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,SAAS9C,4CACP1B,gBAAwB,EACxBuC,KAAgB;IAEhB,MAAMO,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAU,IAAIhC,QAAgB,CAACC,UACnCkD,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMlD,QAAQhB;IAGlC,MAAMmE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CnB,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEmC,oBAAoBC,IAAI,CAACpC;QAC3B,OAAO;YACLiC,kBAAkBI,GAAG,CAACrC;QACxB;IACF;IAEA,MAAMsC,iBAAiB,IAAI5B,MAAMG,SAAS;QACxCf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAiC,kBAAkBvD,GAAG,CAACsB,OACtB,0CAFuE;oBAGvE,MAAMqB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;oBAC1D2C,kBAAkBtC,MAAMmB,KAAK,EAAEH;gBACjC;YACF;YACA,OAAOlB,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAE6B,KAAK,EAAE5B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BiC,kBAAkBO,MAAM,CAACxC;YAC3B;YACA,OAAOG,SAAAA,cAAc,CAACW,GAAG,CAACf,QAAQC,MAAM6B,OAAO5B;QACjD;QACAwC,SAAQ1C,MAAM;YACZ,MAAMsB,aAAa;YACnBuB,6BAA6BvC,MAAMmB,KAAK,EAAEH,YAAYc;YACtD,OAAOO,QAAQD,OAAO,CAAC1C;QACzB;IACF;IAEAJ,aAAamB,GAAG,CAAChD,kBAAkBwE;IACnC,OAAOA;AACT;AAEA,SAASC,UACPf,KAAyB,EACzBH,UAAkB,EAClBwB,iBAAiC;IAEjC,MAAM7E,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc8E,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe/E;QACrBgF,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;IACA,gCAAgC;IAChC,IAAIF,qBAAqBA,kBAAkBI,MAAM,GAAG,GAAG;QACrDL,6BAA6BpB,OAAOH,YAAYwB;IAClD,OAAO;QACLF,kBAAkBnB,OAAOH;IAC3B;AACF;AAEA,MAAMsB,oBAAoBO,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,MAAMP,+BACJM,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACE;AAE9C,SAASD,wBACP3B,KAAyB,EACzBH,UAAkB;IAElB,MAAMgC,SAAS7B,QAAQ,AAAC,OAAO,GAAQ,EAAE,CAAC,IAATA,eAAY;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIf,MACT,UAAG4C,QAAO,KAAK,WAAEhC,YAAW,EAAE,CAAC,GAC7B,EAAC,0DAA0D,CAAC,EAC3D,CAAD,6DAA+D,CAAC,IAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS+B,iCACP5B,KAAyB,EACzBH,UAAkB,EAClBwB,iBAAgC;IAEhC,MAAMQ,SAAS7B,QAAQ,AAAC,OAAO,UAAEA,OAAM,EAAE,CAAC,KAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAIf,MACT,GAAiBY,OAAdgC,QAAO,KAAK,uBAAa,EAAE,CAAC,GAC7B,EAAC,0DAA0D,CAAC,EAC3D,CAAD,+DAAiE,CAAC,GAClE,CAAC,mDAAmD,CAAC,IACrD,UAAGC,4BAA4BT,oBAAmB,EAAE,CAAC,GACrD,EAAC,8DAA8D,CAAC,IAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASS,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWN,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIO,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,AAAC,EAAE,EAAgB,EAAE,CAAC,IAAjBD,UAAU,CAAC,EAAE;QAC3B,KAAK;YACH,OAAO,AAAC,EAAE,SAAEA,UAAU,CAAC,EAAE,EAAC,SAAS,SAAEA,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWN,MAAM,GAAG,GAAGS,IAAK;oBAC9CD,eAAe,AAAC,EAAE,SAAEF,UAAU,CAACG,EAAE,EAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,AAAC,QAAQ,SAAEF,UAAU,CAACA,WAAWN,MAAM,GAAG,EAAE,EAAC,EAAE,CAAC;gBAC/D,OAAOQ;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "InvariantError", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAeg<PERSON>,kBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,eAAe,KAW9B;IAX8B,IAAA,EAC7BC,SAAS,EACTC,YAAY,EACZC,MAAM,EACN,AACAC,QAAQ,EAMT,GAX8B,gDAIgC;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,4BAA4B,EAAE,GACpCN,QAAQ;QACVC,qBAAqBK,6BAA6BX,cAAcQ;QAEhE,MAAM,EAAEI,sBAAsB,EAAE,GAC9BP,QAAQ;QACVE,eAAeK,uBAAuBX,QAAQO;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACT,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD,OAAO;QACL,MAAM,EAAEO,kCAAkC,EAAE,GAC1CR,QAAQ;QACV,MAAMC,qBAAqBO,mCAAmCb;QAC9D,MAAM,EAAEc,4BAA4B,EAAE,GACpCT,QAAQ;QACV,MAAME,eAAeO,6BAA6Bb;QAElD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2511, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "InvariantError", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAcgBA,qBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,kBAAkB,KAWjC;IAXiC,IAAA,EAChCC,SAAS,EACTC,KAAK,EACLC,MAAM,EAENC,AADA,OACO,EAMR,GAXiC,iDAI6B;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,sBAAsB,EAAE,GAC9BL,QAAQ;QACVC,eAAeI,uBAAuBT,QAAQM;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACR,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC,OAAO;QACL,MAAM,EAAEK,4BAA4B,EAAE,GACpCN,QAAQ;QACV,MAAMC,eAAeK,6BAA6BV;QAClD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2565, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": ";;;+BAQaA,YAAAA;;;eAAAA;;;;AAAN,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;QAAKC,MAAK;;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2589, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["AsyncMetadataOutlet", "MetadataOutlet", "promise", "error", "digest", "use", "Suspense", "fallback"], "mappings": ";;;+BAsBgBA,uBAAAA;;;eAAAA;;;;uBApBc;AAG9B,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,OAAO,EAGR,GAJuB;IAKtB,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,OAAAA,GAAG,EAACH;IAC9B,IAAIC,OAAO;QACT,IAAIC,QAAQ;YACV,2FAA2F;YAC3F,mGAAmG;;YACjGD,MAAcC,MAAM,GAAGA;QAC3B;QACA,MAAMD;IACR;IACA,OAAO;AACT;AAEO,SAASH,oBAAoB,KAInC;IAJmC,IAAA,EAClCE,OAAO,EAGR,GAJmC;IAKlC,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACI,OAAAA,QAAQ,EAAA;QAACC,UAAU;kBAClB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACN,gBAAAA;YAAeC,SAASA;;;AAG/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/node_modules/next/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["MetadataBoundary", "OutletBoundary", "ViewportBoundary", "NameSpace", "METADATA_BOUNDARY_NAME", "children", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "slice"], "mappings": ";;;;;;;;;;;;;;;IAkCaA,gBAAgB,EAAA;eAAhBA;;IAUAC,cAAc,EAAA;eAAdA;;IALAC,gBAAgB,EAAA;eAAhBA;;;mCAjCN;AAEP,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCC,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCD,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACE,mBAAAA,oBAAoB,CAAC,EAAE,SAAU,KAIjC;QAJiC,IAAA,EAChCF,QAAQ,EAGT,GAJiC;QAKhC,OAAOA;IACT;AACF;AAEO,MAAML,mBACX,AACA,4DAA4D,oBADoB;AAEhFG,SAAS,CAACC,mBAAAA,sBAAsB,CAACI,KAAK,CAAC,GAAoC;AAEtE,MAAMN,mBACX,AACA,4DAA4D,oBADoB;AAEhFC,SAAS,CAACG,mBAAAA,sBAAsB,CAACE,KAAK,CAAC,GAAoC;AAEtE,MAAMP,iBACX,AACA,4DAA4D,oBADoB;AAEhFE,SAAS,CAACI,mBAAAA,oBAAoB,CAACC,KAAK,CAAC,GAAkC", "ignoreList": [0], "debugId": null}}]}