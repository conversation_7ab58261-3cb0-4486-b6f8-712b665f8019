{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format numbers for display\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value);\n}\n\n// Format currency\nexport function formatCurrency(value: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(value);\n}\n\n// Format percentage\nexport function formatPercentage(value: number, decimals: number = 1): string {\n  return `${formatNumber(value * 100, decimals)}%`;\n}\n\n// Get algorithm icon based on type\nexport function getAlgorithmIcon(algorithmType: string): string {\n  const iconMap: { [key: string]: string } = {\n    'time_series_analyzer': '📈',\n    'forecasting_engine': '🔮',\n    'financial_ratios': '💰',\n    'revenue_agent': '🤖',\n    'scenario_agent': '🎯',\n    'consolidation_agent': '📊',\n    'capex_agent': '🏗️',\n    'web_research_agent': '🔍',\n    'macro_data_agent': '🌍',\n  };\n  \n  return iconMap[algorithmType] || '⚙️';\n}\n\n// Get algorithm color based on category\nexport function getAlgorithmColor(category: string): string {\n  const colorMap: { [key: string]: string } = {\n    'econometric_models': 'bg-blue-100 text-blue-800 border-blue-200',\n    'financial_analysis': 'bg-green-100 text-green-800 border-green-200',\n    'ai_agents': 'bg-purple-100 text-purple-800 border-purple-200',\n    'risk_analysis': 'bg-red-100 text-red-800 border-red-200',\n    'bayesian_models': 'bg-indigo-100 text-indigo-800 border-indigo-200',\n  };\n  \n  return colorMap[category] || 'bg-gray-100 text-gray-800 border-gray-200';\n}\n\n// Debounce function for search\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Generate sample data for demos\nexport function generateSampleTimeSeriesData(points: number = 24): number[] {\n  const data: number[] = [];\n  let value = 1000000; // Start with 1M\n  \n  for (let i = 0; i < points; i++) {\n    // Add trend + seasonality + noise\n    const trend = i * 50000; // Growing trend\n    const seasonality = Math.sin((i / 12) * 2 * Math.PI) * 100000; // Annual cycle\n    const noise = (Math.random() - 0.5) * 200000; // Random variation\n    \n    value = 1000000 + trend + seasonality + noise;\n    data.push(Math.max(0, value)); // Ensure non-negative\n  }\n  \n  return data;\n}\n\n// Generate sample financial data\nexport function generateSampleFinancialData() {\n  return {\n    revenue: 50000000,\n    cost_of_goods_sold: 30000000,\n    operating_expenses: 15000000,\n    total_assets: 100000000,\n    total_liabilities: 60000000,\n    equity: 40000000,\n    cash: 10000000,\n    debt: 25000000,\n    customers: 1000000,\n    arpu: 50,\n    churn_rate: 0.05,\n  };\n}\n\n// Validate data for algorithms\nexport function validateTimeSeriesData(data: any[]): { valid: boolean; message: string } {\n  if (!Array.isArray(data)) {\n    return { valid: false, message: 'Data must be an array' };\n  }\n  \n  if (data.length < 6) {\n    return { valid: false, message: 'Need at least 6 data points for analysis' };\n  }\n  \n  const numericData = data.filter(d => typeof d === 'number' && !isNaN(d));\n  if (numericData.length < data.length * 0.8) {\n    return { valid: false, message: 'At least 80% of data points must be numeric' };\n  }\n  \n  return { valid: true, message: 'Data is valid' };\n}\n\n// Extract business insights from analysis results\nexport function extractBusinessInsights(analysisResult: any): string[] {\n  const insights: string[] = [];\n  \n  if (analysisResult?.business_insights) {\n    analysisResult.business_insights.forEach((insight: any) => {\n      if (insight.finding) {\n        insights.push(insight.finding);\n      }\n    });\n  }\n  \n  if (analysisResult?.summary?.key_finding) {\n    insights.push(analysisResult.summary.key_finding);\n  }\n  \n  if (analysisResult?.recommendations) {\n    analysisResult.recommendations.forEach((rec: any) => {\n      if (typeof rec === 'string') {\n        insights.push(rec);\n      } else if (rec.recommendation) {\n        insights.push(rec.recommendation);\n      }\n    });\n  }\n  \n  return insights;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,aAAa,KAAa;QAAE,WAAA,iEAAmB;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,eAAe,KAAa;QAAE,WAAA,iEAAmB;IAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,iBAAiB,KAAa;QAAE,WAAA,iEAAmB;IACjE,OAAO,AAAC,GAAsC,OAApC,aAAa,QAAQ,KAAK,WAAU;AAChD;AAGO,SAAS,iBAAiB,aAAqB;IACpD,MAAM,UAAqC;QACzC,wBAAwB;QACxB,sBAAsB;QACtB,oBAAoB;QACpB,iBAAiB;QACjB,kBAAkB;QAClB,uBAAuB;QACvB,eAAe;QACf,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,OAAO,OAAO,CAAC,cAAc,IAAI;AACnC;AAGO,SAAS,kBAAkB,QAAgB;IAChD,MAAM,WAAsC;QAC1C,sBAAsB;QACtB,sBAAsB;QACtB,aAAa;QACb,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,OAAO,QAAQ,CAAC,SAAS,IAAI;AAC/B;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;QAA6B,SAAA,iEAAiB;IAC5D,MAAM,OAAiB,EAAE;IACzB,IAAI,QAAQ,SAAS,gBAAgB;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,kCAAkC;QAClC,MAAM,QAAQ,IAAI,OAAO,gBAAgB;QACzC,MAAM,cAAc,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,IAAI,KAAK,EAAE,IAAI,QAAQ,eAAe;QAC9E,MAAM,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,mBAAmB;QAEjE,QAAQ,UAAU,QAAQ,cAAc;QACxC,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,sBAAsB;IACvD;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO;QACL,SAAS;QACT,oBAAoB;QACpB,oBAAoB;QACpB,cAAc;QACd,mBAAmB;QACnB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,WAAW;QACX,MAAM;QACN,YAAY;IACd;AACF;AAGO,SAAS,uBAAuB,IAAW;IAChD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QACxB,OAAO;YAAE,OAAO;YAAO,SAAS;QAAwB;IAC1D;IAEA,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,OAAO;YAAE,OAAO;YAAO,SAAS;QAA2C;IAC7E;IAEA,MAAM,cAAc,KAAK,MAAM,CAAC,CAAA,IAAK,OAAO,MAAM,YAAY,CAAC,MAAM;IACrE,IAAI,YAAY,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK;QAC1C,OAAO;YAAE,OAAO;YAAO,SAAS;QAA8C;IAChF;IAEA,OAAO;QAAE,OAAO;QAAM,SAAS;IAAgB;AACjD;AAGO,SAAS,wBAAwB,cAAmB;QAWrD;IAVJ,MAAM,WAAqB,EAAE;IAE7B,IAAI,2BAAA,qCAAA,eAAgB,iBAAiB,EAAE;QACrC,eAAe,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,QAAQ,OAAO,EAAE;gBACnB,SAAS,IAAI,CAAC,QAAQ,OAAO;YAC/B;QACF;IACF;IAEA,IAAI,2BAAA,sCAAA,0BAAA,eAAgB,OAAO,cAAvB,8CAAA,wBAAyB,WAAW,EAAE;QACxC,SAAS,IAAI,CAAC,eAAe,OAAO,CAAC,WAAW;IAClD;IAEA,IAAI,2BAAA,qCAAA,eAAgB,eAAe,EAAE;QACnC,eAAe,eAAe,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,OAAO,QAAQ,UAAU;gBAC3B,SAAS,IAAI,CAAC;YAChB,OAAO,IAAI,IAAI,cAAc,EAAE;gBAC7B,SAAS,IAAI,CAAC,IAAI,cAAc;YAClC;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/algorithm-library.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport type { AlgorithmsResponse } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, debounce } from '@/lib/utils';\n\ninterface AlgorithmLibraryProps {\n  algorithms: AlgorithmsResponse;\n  onAlgorithmSelect: (algorithm: any, category: string) => void;\n}\n\nexport function AlgorithmLibrary({ algorithms, onAlgorithmSelect }: AlgorithmLibraryProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [sortBy, setSortBy] = useState<'name' | 'category'>('category');\n\n  // Debounced search\n  const debouncedSearch = debounce((term: string) => {\n    setSearchTerm(term);\n  }, 300);\n\n  // Flatten algorithms for filtering and sorting\n  const allAlgorithms = [];\n  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {\n    for (const [algorithmKey, algorithm] of Object.entries(categoryAlgorithms)) {\n      allAlgorithms.push({\n        key: algorithm<PERSON>ey,\n        category: categoryName,\n        ...algorithm,\n      });\n    }\n  }\n\n  // Filter algorithms\n  const filteredAlgorithms = allAlgorithms.filter((algorithm) => {\n    const matchesSearch = searchTerm === '' || \n      algorithm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      algorithm.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      algorithm.capabilities.some((cap: string) => \n        cap.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    \n    const matchesCategory = selectedCategory === 'all' || algorithm.category === selectedCategory;\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  // Sort algorithms\n  const sortedAlgorithms = [...filteredAlgorithms].sort((a, b) => {\n    if (sortBy === 'name') {\n      return a.name.localeCompare(b.name);\n    } else {\n      return a.category.localeCompare(b.category);\n    }\n  });\n\n  const categories = ['all', ...algorithms.categories];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Algorithm Library</h2>\n        <p className=\"text-gray-600\">\n          Discover and explore {algorithms.total_count} advanced algorithms for financial analysis and forecasting.\n        </p>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"flex-1\">\n            <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Algorithms\n            </label>\n            <input\n              type=\"text\"\n              id=\"search\"\n              placeholder=\"Search by name, description, or capabilities...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n              onChange={(e) => debouncedSearch(e.target.value)}\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Category\n            </label>\n            <select\n              id=\"category\"\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n            >\n              {categories.map((category) => (\n                <option key={category} value={category}>\n                  {category === 'all' ? 'All Categories' : category.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Sort */}\n          <div>\n            <label htmlFor=\"sort\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Sort By\n            </label>\n            <select\n              id=\"sort\"\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value as 'name' | 'category')}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n            >\n              <option value=\"category\">Category</option>\n              <option value=\"name\">Name</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mt-4 text-sm text-gray-600\">\n          Showing {sortedAlgorithms.length} of {algorithms.total_count} algorithms\n        </div>\n      </div>\n\n      {/* Algorithm Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {sortedAlgorithms.map((algorithm) => (\n          <div\n            key={`${algorithm.category}-${algorithm.key}`}\n            className=\"bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer\"\n            onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}\n          >\n            <div className=\"p-6\">\n              {/* Header */}\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"text-3xl\">{getAlgorithmIcon(algorithm.key)}</div>\n                <span className={`px-3 py-1 text-xs font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n                  {algorithm.category.replace('_', ' ')}\n                </span>\n              </div>\n\n              {/* Content */}\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{algorithm.name}</h3>\n              <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">{algorithm.description}</p>\n\n              {/* Capabilities */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-2\">CAPABILITIES</h4>\n                <div className=\"flex flex-wrap gap-1\">\n                  {algorithm.capabilities.slice(0, 3).map((capability: string) => (\n                    <span\n                      key={capability}\n                      className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\"\n                    >\n                      {capability.replace('_', ' ')}\n                    </span>\n                  ))}\n                  {algorithm.capabilities.length > 3 && (\n                    <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\">\n                      +{algorithm.capabilities.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* Input Requirements */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-2\">REQUIRES</h4>\n                <div className=\"flex flex-wrap gap-1\">\n                  {algorithm.input_requirements.map((requirement: string) => (\n                    <span\n                      key={requirement}\n                      className=\"px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded border border-blue-200\"\n                    >\n                      {requirement.replace('_', ' ')}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Output Format */}\n              <div className=\"mb-4\">\n                <h4 className=\"text-xs font-medium text-gray-700 mb-1\">OUTPUT</h4>\n                <span className=\"text-xs text-gray-600\">\n                  {algorithm.output_format.replace('_', ' ')}\n                </span>\n              </div>\n\n              {/* Action Button */}\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  onAlgorithmSelect(algorithm, algorithm.category);\n                }}\n                className=\"w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium\"\n              >\n                Configure & Run\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Empty State */}\n      {sortedAlgorithms.length === 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm p-12 text-center\">\n          <div className=\"text-gray-400 text-6xl mb-4\">🔍</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No algorithms found</h3>\n          <p className=\"text-gray-600 mb-4\">\n            Try adjusting your search terms or category filter.\n          </p>\n          <button\n            onClick={() => {\n              setSearchTerm('');\n              setSelectedCategory('all');\n            }}\n            className=\"bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors\"\n          >\n            Clear Filters\n          </button>\n        </div>\n      )}\n\n      {/* Category Overview */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Categories Overview</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {algorithms.categories.map((category) => {\n            const categoryAlgorithms = Object.keys(algorithms.algorithms[category as keyof typeof algorithms.algorithms]).length;\n            return (\n              <div\n                key={category}\n                className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${\n                  selectedCategory === category\n                    ? 'border-indigo-500 bg-indigo-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => setSelectedCategory(category)}\n              >\n                <div className={`text-sm font-medium mb-1 ${getAlgorithmColor(category).split(' ')[1]}`}>\n                  {category.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                </div>\n                <div className=\"text-2xl font-bold text-gray-900\">{categoryAlgorithms}</div>\n                <div className=\"text-xs text-gray-600\">algorithms</div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAWO,SAAS,iBAAiB,KAAwD;QAAxD,EAAE,UAAU,EAAE,iBAAiB,EAAyB,GAAxD;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAE1D,mBAAmB;IACnB,MAAM,kBAAkB,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QAChC,cAAc;IAChB,GAAG;IAEH,+CAA+C;IAC/C,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,CAAC,cAAc,mBAAmB,IAAI,OAAO,OAAO,CAAC,WAAW,UAAU,EAAG;QACtF,KAAK,MAAM,CAAC,cAAc,UAAU,IAAI,OAAO,OAAO,CAAC,oBAAqB;YAC1E,cAAc,IAAI,CAAC;gBACjB,KAAK;gBACL,UAAU;gBACV,GAAG,SAAS;YACd;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,qBAAqB,cAAc,MAAM,CAAC,CAAC;QAC/C,MAAM,gBAAgB,eAAe,MACnC,UAAU,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,UAAU,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,UAAU,YAAY,CAAC,IAAI,CAAC,CAAC,MAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAGrD,MAAM,kBAAkB,qBAAqB,SAAS,UAAU,QAAQ,KAAK;QAE7E,OAAO,iBAAiB;IAC1B;IAEA,kBAAkB;IAClB,MAAM,mBAAmB;WAAI;KAAmB,CAAC,IAAI,CAAC,CAAC,GAAG;QACxD,IAAI,WAAW,QAAQ;YACrB,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;QAC5C;IACF;IAEA,MAAM,aAAa;QAAC;WAAU,WAAW,UAAU;KAAC;IAEpD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;;4BAAgB;4BACL,WAAW,WAAW;4BAAC;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAS,WAAU;kDAA+C;;;;;;kDAGjF,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,aAAY;wCACZ,WAAU;wCACV,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAKnD,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAA+C;;;;;;kDAGnF,6LAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACnD,WAAU;kDAET,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gDAAsB,OAAO;0DAC3B,aAAa,QAAQ,mBAAmB,SAAS,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;+CAD5F;;;;;;;;;;;;;;;;0CAQnB,6LAAC;;kDACC,6LAAC;wCAAM,SAAQ;wCAAO,WAAU;kDAA+C;;;;;;kDAG/E,6LAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;kCAM3B,6LAAC;wBAAI,WAAU;;4BAA6B;4BACjC,iBAAiB,MAAM;4BAAC;4BAAK,WAAW,WAAW;4BAAC;;;;;;;;;;;;;0BAKjE,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,0BACrB,6LAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,kBAAkB,WAAW,UAAU,QAAQ;kCAE9D,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;sDACzD,6LAAC;4CAAK,WAAW,AAAC,8CAAmF,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;sDAChG,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAKrC,6LAAC;oCAAG,WAAU;8CAA4C,UAAU,IAAI;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAA2C,UAAU,WAAW;;;;;;8CAG7E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;;gDACZ,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,2BACvC,6LAAC;wDAEC,WAAU;kEAET,WAAW,OAAO,CAAC,KAAK;uDAHpB;;;;;gDAMR,UAAU,YAAY,CAAC,MAAM,GAAG,mBAC/B,6LAAC;oDAAK,WAAU;;wDAAsD;wDAClE,UAAU,YAAY,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,4BACjC,6LAAC;oDAEC,WAAU;8DAET,YAAY,OAAO,CAAC,KAAK;mDAHrB;;;;;;;;;;;;;;;;8CAUb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAK,WAAU;sDACb,UAAU,aAAa,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAK1C,6LAAC;oCACC,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,kBAAkB,WAAW,UAAU,QAAQ;oCACjD;oCACA,WAAU;8CACX;;;;;;;;;;;;uBAnEE,AAAC,GAAwB,OAAtB,UAAU,QAAQ,EAAC,KAAiB,OAAd,UAAU,GAAG;;;;;;;;;;YA4EhD,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBACC,SAAS;4BACP,cAAc;4BACd,oBAAoB;wBACtB;wBACA,WAAU;kCACX;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC;4BAC1B,MAAM,qBAAqB,OAAO,IAAI,CAAC,WAAW,UAAU,CAAC,SAA+C,EAAE,MAAM;4BACpH,qBACE,6LAAC;gCAEC,WAAW,AAAC,4DAIX,OAHC,qBAAqB,WACjB,mCACA;gCAEN,SAAS,IAAM,oBAAoB;;kDAEnC,6LAAC;wCAAI,WAAW,AAAC,4BAAqE,OAA1C,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE;kDAClF,SAAS,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;kDAEjE,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;+BAZlC;;;;;wBAeX;;;;;;;;;;;;;;;;;;AAKV;GAnPgB;KAAA", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API client for OneOptimizer backend\n */\n\nimport axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Types for API responses\nexport interface Algorithm {\n  name: string;\n  description: string;\n  capabilities: string[];\n  input_requirements: string[];\n  output_format: string;\n}\n\nexport interface AlgorithmCategory {\n  [key: string]: Algorithm;\n}\n\nexport interface AlgorithmsResponse {\n  success: boolean;\n  algorithms: {\n    econometric_models: AlgorithmCategory;\n    financial_analysis: AlgorithmCategory;\n    ai_agents: AlgorithmCategory;\n  };\n  total_count: number;\n  categories: string[];\n}\n\nexport interface AnalysisRequest {\n  data: any;\n  context?: string;\n  periods?: number;\n  method?: 'econometric' | 'ai' | 'hybrid';\n}\n\nexport interface AnalysisResponse {\n  success: boolean;\n  analysis?: any;\n  forecast?: any;\n  data?: any;\n  algorithm: string;\n  timestamp: string;\n  message?: string;\n}\n\n// API functions\nexport const apiClient = {\n  // Get available algorithms\n  async getAlgorithms(): Promise<AlgorithmsResponse> {\n    const response = await api.get('/api/algorithms');\n    return response.data;\n  },\n\n  // Health check\n  async healthCheck() {\n    const response = await api.get('/api/health');\n    return response.data;\n  },\n\n  // Time series analysis\n  async analyzeTimeSeries(request: AnalysisRequest): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/time-series', request);\n    return response.data;\n  },\n\n  // Forecasting\n  async createForecast(request: AnalysisRequest): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/forecast', request);\n    return response.data;\n  },\n\n  // Financial ratios analysis\n  async analyzeFinancialRatios(request: {\n    financial_data: any;\n    industry_type?: string;\n  }): Promise<AnalysisResponse> {\n    const response = await api.post('/api/analysis/financial-ratios', request);\n    return response.data;\n  },\n\n  // AI Agents\n  async runRevenueAgent(request: any): Promise<AnalysisResponse> {\n    const response = await api.post('/api/agents/revenue', request);\n    return response.data;\n  },\n\n  async runScenarioAgent(request: any): Promise<AnalysisResponse> {\n    const response = await api.post('/api/agents/scenario', request);\n    return response.data;\n  },\n\n  // Wizard processes\n  async runWizardProcess(request: {\n    process_type: string;\n    [key: string]: any;\n  }): Promise<AnalysisResponse> {\n    const response = await api.post('/api/wizard/process', request);\n    return response.data;\n  },\n};\n\n// Error handling\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('API Error:', error.response?.data || error.message);\n    throw error;\n  }\n);\n\nexport default apiClient;\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAIoB;AAFrB;;AAEA,MAAM,eAAe,6DAAmC;AAEjD,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4CO,MAAM,YAAY;IACvB,2BAA2B;IAC3B,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM;QACJ,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,MAAM,mBAAkB,OAAwB;QAC9C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,6BAA6B;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,MAAM,gBAAe,OAAwB;QAC3C,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,0BAA0B;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,wBAAuB,OAG5B;QACC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,kCAAkC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,MAAM,iBAAgB,OAAY;QAChC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAiB,OAAY;QACjC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,wBAAwB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,MAAM,kBAAiB,OAGtB;QACC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;QACvD,OAAO,SAAS,IAAI;IACtB;AACF;AAEA,iBAAiB;AACjB,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC,WAAa,UACd,CAAC;QAC6B;IAA5B,QAAQ,KAAK,CAAC,cAAc,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI,MAAM,OAAO;IACjE,MAAM;AACR;uCAGa", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/analysis-workspace.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { apiClient } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData, generateSampleFinancialData, validateTimeSeriesData, extractBusinessInsights } from '@/lib/utils';\n\ninterface AnalysisWorkspaceProps {\n  algorithm: any;\n  onBack: () => void;\n}\n\nexport function AnalysisWorkspace({ algorithm, onBack }: AnalysisWorkspaceProps) {\n  const [isRunning, setIsRunning] = useState(false);\n  const [results, setResults] = useState<any>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [parameters, setParameters] = useState<any>({});\n\n  const handleRunAnalysis = async () => {\n    try {\n      setIsRunning(true);\n      setError(null);\n      \n      let response;\n      \n      // Generate appropriate sample data based on algorithm type\n      if (algorithm.key === 'time_series_analyzer') {\n        const sampleData = generateSampleTimeSeriesData(24);\n        const validation = validateTimeSeriesData(sampleData);\n        \n        if (!validation.valid) {\n          throw new Error(validation.message);\n        }\n        \n        response = await apiClient.analyzeTimeSeries({\n          data: sampleData,\n          context: parameters.context || 'revenue',\n        });\n      } else if (algorithm.key === 'forecasting_engine') {\n        const sampleData = generateSampleTimeSeriesData(24);\n        response = await apiClient.createForecast({\n          data: sampleData,\n          periods: parameters.periods || 12,\n          method: parameters.method || 'econometric',\n          context: parameters.context || 'revenue',\n        });\n      } else if (algorithm.key === 'financial_ratios') {\n        const sampleData = generateSampleFinancialData();\n        response = await apiClient.analyzeFinancialRatios({\n          financial_data: sampleData,\n          industry_type: parameters.industry_type || 'telecom',\n        });\n      } else if (algorithm.key === 'revenue_agent') {\n        response = await apiClient.runRevenueAgent({\n          business_units: parameters.business_units || ['Virgin Mobile Chile', 'Virgin Mobile Colombia'],\n          forecast_periods: parameters.periods || 12,\n          scenarios: parameters.scenarios || ['Base', 'Optimistic', 'Pessimistic'],\n        });\n      } else if (algorithm.key === 'scenario_agent') {\n        response = await apiClient.runScenarioAgent({\n          base_assumptions: parameters.base_assumptions || {\n            revenue_growth: 0.15,\n            cost_inflation: 0.05,\n            market_expansion: 0.10\n          },\n          risk_parameters: parameters.risk_parameters || {\n            economic_volatility: 0.2,\n            competitive_pressure: 0.15,\n            regulatory_risk: 0.1\n          },\n          simulation_count: parameters.simulation_count || 1000,\n        });\n      } else {\n        // Generic algorithm execution\n        response = await apiClient.runWizardProcess({\n          process_type: 'comprehensive',\n          algorithm: algorithm.key,\n          parameters: parameters,\n        });\n      }\n      \n      setResults(response);\n    } catch (err: any) {\n      setError(err.response?.data?.detail || err.message || 'Analysis failed');\n      console.error('Analysis error:', err);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const handleParameterChange = (key: string, value: any) => {\n    setParameters(prev => ({ ...prev, [key]: value }));\n  };\n\n  const renderParameterControls = () => {\n    const controls = [];\n    \n    // Common parameters based on algorithm type\n    if (algorithm.input_requirements.includes('time_series_data')) {\n      controls.push(\n        <div key=\"context\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Business Context\n          </label>\n          <select\n            value={parameters.context || 'revenue'}\n            onChange={(e) => handleParameterChange('context', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"revenue\">Revenue</option>\n            <option value=\"costs\">Costs</option>\n            <option value=\"customers\">Customers</option>\n            <option value=\"arpu\">ARPU</option>\n          </select>\n        </div>\n      );\n    }\n    \n    if (algorithm.input_requirements.includes('forecast_periods')) {\n      controls.push(\n        <div key=\"periods\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Forecast Periods\n          </label>\n          <input\n            type=\"number\"\n            min=\"1\"\n            max=\"60\"\n            value={parameters.periods || 12}\n            onChange={(e) => handleParameterChange('periods', parseInt(e.target.value))}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          />\n        </div>\n      );\n    }\n    \n    if (algorithm.key === 'forecasting_engine') {\n      controls.push(\n        <div key=\"method\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Forecasting Method\n          </label>\n          <select\n            value={parameters.method || 'econometric'}\n            onChange={(e) => handleParameterChange('method', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"econometric\">Econometric</option>\n            <option value=\"ai\">AI-Powered</option>\n            <option value=\"hybrid\">Hybrid</option>\n          </select>\n        </div>\n      );\n    }\n    \n    if (algorithm.key === 'financial_ratios') {\n      controls.push(\n        <div key=\"industry\" className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Industry Type\n          </label>\n          <select\n            value={parameters.industry_type || 'telecom'}\n            onChange={(e) => handleParameterChange('industry_type', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n          >\n            <option value=\"telecom\">Telecommunications</option>\n            <option value=\"fintech\">Fintech</option>\n            <option value=\"general\">General</option>\n          </select>\n        </div>\n      );\n    }\n    \n    return controls;\n  };\n\n  const renderResults = () => {\n    if (!results) return null;\n    \n    const insights = extractBusinessInsights(results.analysis || results.data || results);\n    \n    return (\n      <div className=\"space-y-6\">\n        {/* Success Message */}\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <div className=\"text-green-600 text-xl mr-3\">✅</div>\n            <div>\n              <h4 className=\"font-medium text-green-800\">Analysis Complete</h4>\n              <p className=\"text-green-700 text-sm\">\n                {algorithm.name} executed successfully at {new Date(results.timestamp).toLocaleTimeString()}\n              </p>\n            </div>\n          </div>\n        </div>\n        \n        {/* Business Insights */}\n        {insights.length > 0 && (\n          <div className=\"bg-white rounded-lg border p-6\">\n            <h4 className=\"font-semibold text-gray-900 mb-4\">💡 Key Insights</h4>\n            <div className=\"space-y-3\">\n              {insights.map((insight, index) => (\n                <div key={index} className=\"flex items-start\">\n                  <div className=\"text-blue-600 mr-3 mt-1\">•</div>\n                  <p className=\"text-gray-700\">{insight}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n        \n        {/* Raw Results */}\n        <div className=\"bg-white rounded-lg border p-6\">\n          <h4 className=\"font-semibold text-gray-900 mb-4\">📊 Detailed Results</h4>\n          <div className=\"bg-gray-50 rounded-lg p-4 overflow-auto max-h-96\">\n            <pre className=\"text-sm text-gray-700\">\n              {JSON.stringify(results, null, 2)}\n            </pre>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <button\n              onClick={onBack}\n              className=\"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              ← Back\n            </button>\n            <div className=\"text-3xl mr-4\">{getAlgorithmIcon(algorithm.key)}</div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-gray-900\">{algorithm.name}</h2>\n              <p className=\"text-gray-600\">{algorithm.description}</p>\n            </div>\n          </div>\n          <span className={`px-3 py-1 text-sm font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n            {algorithm.category.replace('_', ' ')}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Configuration Panel */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">⚙️ Configuration</h3>\n            \n            {/* Algorithm Info */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">CAPABILITIES</h4>\n              <div className=\"space-y-1\">\n                {algorithm.capabilities.map((capability: string) => (\n                  <div key={capability} className=\"text-sm text-gray-600\">\n                    • {capability.replace('_', ' ')}\n                  </div>\n                ))}\n              </div>\n            </div>\n            \n            {/* Parameter Controls */}\n            <div className=\"mb-6\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-3\">PARAMETERS</h4>\n              {renderParameterControls()}\n            </div>\n            \n            {/* Sample Data Info */}\n            <div className=\"mb-6 p-3 bg-blue-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-800 mb-1\">📊 Sample Data</h4>\n              <p className=\"text-xs text-blue-700\">\n                This demo uses generated sample data. In production, you would upload your own data files.\n              </p>\n            </div>\n            \n            {/* Run Button */}\n            <button\n              onClick={handleRunAnalysis}\n              disabled={isRunning}\n              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${\n                isRunning\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-indigo-600 text-white hover:bg-indigo-700'\n              }`}\n            >\n              {isRunning ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Running Analysis...\n                </div>\n              ) : (\n                '🚀 Run Analysis'\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Results Panel */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"bg-white rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">📈 Results</h3>\n            \n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-red-600 text-xl mr-3\">❌</div>\n                  <div>\n                    <h4 className=\"font-medium text-red-800\">Analysis Failed</h4>\n                    <p className=\"text-red-700 text-sm\">{error}</p>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            {!results && !error && !isRunning && (\n              <div className=\"text-center py-12\">\n                <div className=\"text-gray-400 text-6xl mb-4\">📊</div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Analyze</h4>\n                <p className=\"text-gray-600\">\n                  Configure your parameters and click \"Run Analysis\" to see results.\n                </p>\n              </div>\n            )}\n            \n            {isRunning && (\n              <div className=\"text-center py-12\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n                <h4 className=\"text-lg font-medium text-gray-900 mb-2\">Running Analysis</h4>\n                <p className=\"text-gray-600\">\n                  {algorithm.name} is processing your data...\n                </p>\n              </div>\n            )}\n            \n            {results && renderResults()}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,kBAAkB,KAA6C;QAA7C,EAAE,SAAS,EAAE,MAAM,EAA0B,GAA7C;;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAEnD,MAAM,oBAAoB;QACxB,IAAI;YACF,aAAa;YACb,SAAS;YAET,IAAI;YAEJ,2DAA2D;YAC3D,IAAI,UAAU,GAAG,KAAK,wBAAwB;gBAC5C,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,+BAA4B,AAAD,EAAE;gBAChD,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;gBAE1C,IAAI,CAAC,WAAW,KAAK,EAAE;oBACrB,MAAM,IAAI,MAAM,WAAW,OAAO;gBACpC;gBAEA,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;oBAC3C,MAAM;oBACN,SAAS,WAAW,OAAO,IAAI;gBACjC;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,sBAAsB;gBACjD,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,+BAA4B,AAAD,EAAE;gBAChD,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;oBACxC,MAAM;oBACN,SAAS,WAAW,OAAO,IAAI;oBAC/B,QAAQ,WAAW,MAAM,IAAI;oBAC7B,SAAS,WAAW,OAAO,IAAI;gBACjC;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,oBAAoB;gBAC/C,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;gBAC7C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;oBAChD,gBAAgB;oBAChB,eAAe,WAAW,aAAa,IAAI;gBAC7C;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,iBAAiB;gBAC5C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;oBACzC,gBAAgB,WAAW,cAAc,IAAI;wBAAC;wBAAuB;qBAAyB;oBAC9F,kBAAkB,WAAW,OAAO,IAAI;oBACxC,WAAW,WAAW,SAAS,IAAI;wBAAC;wBAAQ;wBAAc;qBAAc;gBAC1E;YACF,OAAO,IAAI,UAAU,GAAG,KAAK,kBAAkB;gBAC7C,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;oBAC1C,kBAAkB,WAAW,gBAAgB,IAAI;wBAC/C,gBAAgB;wBAChB,gBAAgB;wBAChB,kBAAkB;oBACpB;oBACA,iBAAiB,WAAW,eAAe,IAAI;wBAC7C,qBAAqB;wBACrB,sBAAsB;wBACtB,iBAAiB;oBACnB;oBACA,kBAAkB,WAAW,gBAAgB,IAAI;gBACnD;YACF,OAAO;gBACL,8BAA8B;gBAC9B,WAAW,MAAM,oHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;oBAC1C,cAAc;oBACd,WAAW,UAAU,GAAG;oBACxB,YAAY;gBACd;YACF;YAEA,WAAW;QACb,EAAE,OAAO,KAAU;gBACR,oBAAA;YAAT,SAAS,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI,IAAI,OAAO,IAAI;YACtD,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,0BAA0B;QAC9B,MAAM,WAAW,EAAE;QAEnB,4CAA4C;QAC5C,IAAI,UAAU,kBAAkB,CAAC,QAAQ,CAAC,qBAAqB;YAC7D,SAAS,IAAI,eACX,6LAAC;gBAAkB,WAAU;;kCAC3B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,OAAO,IAAI;wBAC7B,UAAU,CAAC,IAAM,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK;wBAChE,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6LAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,6LAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;;eAZhB;;;;;QAgBb;QAEA,IAAI,UAAU,kBAAkB,CAAC,QAAQ,CAAC,qBAAqB;YAC7D,SAAS,IAAI,eACX,6LAAC;gBAAkB,WAAU;;kCAC3B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,OAAO,WAAW,OAAO,IAAI;wBAC7B,UAAU,CAAC,IAAM,sBAAsB,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBACzE,WAAU;;;;;;;eAVL;;;;;QAcb;QAEA,IAAI,UAAU,GAAG,KAAK,sBAAsB;YAC1C,SAAS,IAAI,eACX,6LAAC;gBAAiB,WAAU;;kCAC1B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,MAAM,IAAI;wBAC5B,UAAU,CAAC,IAAM,sBAAsB,UAAU,EAAE,MAAM,CAAC,KAAK;wBAC/D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,6LAAC;gCAAO,OAAM;0CAAK;;;;;;0CACnB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;;;;;;;;eAXlB;;;;;QAeb;QAEA,IAAI,UAAU,GAAG,KAAK,oBAAoB;YACxC,SAAS,IAAI,eACX,6LAAC;gBAAmB,WAAU;;kCAC5B,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO,WAAW,aAAa,IAAI;wBACnC,UAAU,CAAC,IAAM,sBAAsB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBACtE,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;;;;;;;;eAXnB;;;;;QAeb;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,QAAQ,IAAI,QAAQ,IAAI,IAAI;QAE7E,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAE,WAAU;;4CACV,UAAU,IAAI;4CAAC;4CAA2B,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;gBAOhG,SAAS,MAAM,GAAG,mBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDAA0B;;;;;;sDACzC,6LAAC;4CAAE,WAAU;sDAAiB;;;;;;;mCAFtB;;;;;;;;;;;;;;;;8BAUlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,KAAK,SAAS,CAAC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;IAM3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAI,WAAU;8CAAiB,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;8CAC9D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC,UAAU,IAAI;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAAiB,UAAU,WAAW;;;;;;;;;;;;;;;;;;sCAGvD,6LAAC;4BAAK,WAAW,AAAC,8CAAmF,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;sCAChG,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,2BAC3B,6LAAC;oDAAqB,WAAU;;wDAAwB;wDACnD,WAAW,OAAO,CAAC,KAAK;;mDADnB;;;;;;;;;;;;;;;;8CAQhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;wCACtD;;;;;;;8CAIH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAW,AAAC,6DAIX,OAHC,YACI,iDACA;8CAGL,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAuE;;;;;;+CAIxF;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;gCAExD,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA4B;;;;;;0DAC3C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2B;;;;;;kEACzC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;gCAM5C,CAAC,WAAW,CAAC,SAAS,CAAC,2BACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAMhC,2BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;;gDACV,UAAU,IAAI;gDAAC;;;;;;;;;;;;;gCAKrB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GA9UgB;KAAA", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport type { AlgorithmsResponse } from '@/lib/api';\nimport { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData } from '@/lib/utils';\n\ninterface DashboardProps {\n  algorithms: AlgorithmsResponse | null;\n  onAlgorithmSelect: (algorithm: any, category: string) => void;\n  onViewChange: (view: 'dashboard' | 'algorithms' | 'workspace') => void;\n}\n\nexport function Dashboard({ algorithms, onAlgorithmSelect, onViewChange }: DashboardProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n\n  if (!algorithms) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-500\">Loading algorithms...</div>\n      </div>\n    );\n  }\n\n  const totalAlgorithms = algorithms.total_count;\n  const categories = algorithms.categories;\n\n  // Get featured algorithms (first 3 from each category)\n  const featuredAlgorithms = [];\n  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {\n    const algorithmEntries = Object.entries(categoryAlgorithms).slice(0, 2);\n    for (const [algorithmKey, algorithm] of algorithmEntries) {\n      featuredAlgorithms.push({\n        key: algorithmKey,\n        category: categoryName,\n        ...algorithm,\n      });\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Welcome to OneOptimizer v2.0\n          </h2>\n          <p className=\"text-lg text-gray-600 mb-6\">\n            AI-Powered Budgeting System with Advanced Analytics\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🧮</div>\n              <h3 className=\"font-semibold text-gray-900\">Advanced Algorithms</h3>\n              <p className=\"text-gray-600\">Bayesian forecasting, econometric models, and AI agents</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">📊</div>\n              <h3 className=\"font-semibold text-gray-900\">Interactive Analysis</h3>\n              <p className=\"text-gray-600\">Real-time parameter adjustment and visualization</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">🎯</div>\n              <h3 className=\"font-semibold text-gray-900\">Business Insights</h3>\n              <p className=\"text-gray-600\">Clear recommendations and actionable insights</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🧮</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">{totalAlgorithms}</div>\n              <div className=\"text-sm text-gray-600\">Available Algorithms</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">📂</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">{categories.length}</div>\n              <div className=\"text-sm text-gray-600\">Algorithm Categories</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">🤖</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">7</div>\n              <div className=\"text-sm text-gray-600\">AI Agents</div>\n            </div>\n          </div>\n        </div>\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"text-2xl mr-3\">⚡</div>\n            <div>\n              <div className=\"text-2xl font-bold text-gray-900\">Ready</div>\n              <div className=\"text-sm text-gray-600\">System Status</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button\n            onClick={() => onViewChange('algorithms')}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-400 hover:bg-indigo-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">🔍</div>\n            <h4 className=\"font-medium text-gray-900\">Explore Algorithms</h4>\n            <p className=\"text-sm text-gray-600\">Browse and discover available analysis algorithms</p>\n          </button>\n          <button\n            onClick={() => {\n              // Select a sample algorithm for demo\n              const sampleAlgorithm = featuredAlgorithms[0];\n              if (sampleAlgorithm) {\n                onAlgorithmSelect(sampleAlgorithm, sampleAlgorithm.category);\n              }\n            }}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">🚀</div>\n            <h4 className=\"font-medium text-gray-900\">Quick Analysis</h4>\n            <p className=\"text-sm text-gray-600\">Start analyzing with sample data</p>\n          </button>\n          <button\n            onClick={() => {\n              // Generate sample data and start analysis\n              const sampleData = generateSampleTimeSeriesData();\n              console.log('Generated sample data:', sampleData);\n            }}\n            className=\"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-left\"\n          >\n            <div className=\"text-2xl mb-2\">📊</div>\n            <h4 className=\"font-medium text-gray-900\">Sample Data</h4>\n            <p className=\"text-sm text-gray-600\">Generate sample data for testing</p>\n          </button>\n        </div>\n      </div>\n\n      {/* Featured Algorithms */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Featured Algorithms</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {featuredAlgorithms.slice(0, 6).map((algorithm) => (\n            <div\n              key={`${algorithm.category}-${algorithm.key}`}\n              className=\"border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer\"\n              onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}\n            >\n              <div className=\"flex items-start justify-between mb-3\">\n                <div className=\"text-2xl\">{getAlgorithmIcon(algorithm.key)}</div>\n                <span className={`px-2 py-1 text-xs rounded-full ${getAlgorithmColor(algorithm.category)}`}>\n                  {algorithm.category.replace('_', ' ')}\n                </span>\n              </div>\n              <h4 className=\"font-medium text-gray-900 mb-2\">{algorithm.name}</h4>\n              <p className=\"text-sm text-gray-600 mb-3\">{algorithm.description}</p>\n              <div className=\"flex flex-wrap gap-1\">\n                {algorithm.capabilities.slice(0, 2).map((capability: string) => (\n                  <span\n                    key={capability}\n                    className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\"\n                  >\n                    {capability.replace('_', ' ')}\n                  </span>\n                ))}\n                {algorithm.capabilities.length > 2 && (\n                  <span className=\"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded\">\n                    +{algorithm.capabilities.length - 2} more\n                  </span>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n        <div className=\"mt-6 text-center\">\n          <button\n            onClick={() => onViewChange('algorithms')}\n            className=\"bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors\"\n          >\n            View All Algorithms\n          </button>\n        </div>\n      </div>\n\n      {/* Getting Started */}\n      <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-sm p-6 text-white\">\n        <h3 className=\"text-xl font-semibold mb-4\">Getting Started</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <h4 className=\"font-medium mb-2\">1. Choose Your Algorithm</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Browse our library of advanced algorithms including Bayesian forecasting, \n              econometric models, and AI agents.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">2. Configure Parameters</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Adjust algorithm parameters in real-time with our interactive \n              configuration interface.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">3. Analyze Results</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              View interactive visualizations, confidence intervals, and \n              business insights from your analysis.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">4. Export & Share</h4>\n            <p className=\"text-indigo-100 text-sm\">\n              Export results in multiple formats and share insights with \n              your team.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAYO,SAAS,UAAU,KAA+D;QAA/D,EAAE,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAkB,GAA/D;;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,MAAM,kBAAkB,WAAW,WAAW;IAC9C,MAAM,aAAa,WAAW,UAAU;IAExC,uDAAuD;IACvD,MAAM,qBAAqB,EAAE;IAC7B,KAAK,MAAM,CAAC,cAAc,mBAAmB,IAAI,OAAO,OAAO,CAAC,WAAW,UAAU,EAAG;QACtF,MAAM,mBAAmB,OAAO,OAAO,CAAC,oBAAoB,KAAK,CAAC,GAAG;QACrE,KAAK,MAAM,CAAC,cAAc,UAAU,IAAI,iBAAkB;YACxD,mBAAmB,IAAI,CAAC;gBACtB,KAAK;gBACL,UAAU;gBACV,GAAG,SAAS;YACd;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAoC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAoC,WAAW,MAAM;;;;;;sDACpE,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAmC;;;;;;sDAClD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,SAAS;oCACP,qCAAqC;oCACrC,MAAM,kBAAkB,kBAAkB,CAAC,EAAE;oCAC7C,IAAI,iBAAiB;wCACnB,kBAAkB,iBAAiB,gBAAgB,QAAQ;oCAC7D;gCACF;gCACA,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCACC,SAAS;oCACP,0CAA0C;oCAC1C,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,+BAA4B,AAAD;oCAC9C,QAAQ,GAAG,CAAC,0BAA0B;gCACxC;gCACA,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,mBAAmB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,0BACnC,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,kBAAkB,WAAW,UAAU,QAAQ;;kDAE9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,GAAG;;;;;;0DACzD,6LAAC;gDAAK,WAAW,AAAC,kCAAuE,OAAtC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;0DACpF,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kDAGrC,6LAAC;wCAAG,WAAU;kDAAkC,UAAU,IAAI;;;;;;kDAC9D,6LAAC;wCAAE,WAAU;kDAA8B,UAAU,WAAW;;;;;;kDAChE,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,2BACvC,6LAAC;oDAEC,WAAU;8DAET,WAAW,OAAO,CAAC,KAAK;mDAHpB;;;;;4CAMR,UAAU,YAAY,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gDAAK,WAAU;;oDAAsD;oDAClE,UAAU,YAAY,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;+BAvBrC,AAAC,GAAwB,OAAtB,UAAU,QAAQ,EAAC,KAAiB,OAAd,UAAU,GAAG;;;;;;;;;;kCA8BjD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GA7NgB;KAAA", "debugId": null}}, {"offset": {"line": 2391, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/components/navigation.tsx"], "sourcesContent": ["'use client';\n\ninterface NavigationProps {\n  currentView: 'dashboard' | 'algorithms' | 'workspace';\n  onViewChange: (view: 'dashboard' | 'algorithms' | 'workspace') => void;\n}\n\nexport function Navigation({ currentView, onViewChange }: NavigationProps) {\n  const navItems = [\n    { id: 'dashboard', label: 'Dashboard', icon: '📊' },\n    { id: 'algorithms', label: 'Algorithm Library', icon: '🧮' },\n    { id: 'workspace', label: 'Analysis Workspace', icon: '⚙️' },\n  ] as const;\n\n  return (\n    <nav className=\"flex space-x-1\">\n      {navItems.map((item) => (\n        <button\n          key={item.id}\n          onClick={() => onViewChange(item.id)}\n          className={`\n            flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors\n            ${currentView === item.id\n              ? 'bg-indigo-100 text-indigo-700'\n              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'\n            }\n          `}\n        >\n          <span className=\"mr-2\">{item.icon}</span>\n          {item.label}\n        </button>\n      ))}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOO,SAAS,WAAW,KAA8C;QAA9C,EAAE,WAAW,EAAE,YAAY,EAAmB,GAA9C;IACzB,MAAM,WAAW;QACf;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM;QAAK;QAClD;YAAE,IAAI;YAAc,OAAO;YAAqB,MAAM;QAAK;QAC3D;YAAE,IAAI;YAAa,OAAO;YAAsB,MAAM;QAAK;KAC5D;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gBAEC,SAAS,IAAM,aAAa,KAAK,EAAE;gBACnC,WAAW,AAAC,2GAKT,OAHC,gBAAgB,KAAK,EAAE,GACrB,kCACA,uDACH;;kCAGH,6LAAC;wBAAK,WAAU;kCAAQ,KAAK,IAAI;;;;;;oBAChC,KAAK,KAAK;;eAXN,KAAK,EAAE;;;;;;;;;;AAgBtB;KA3BgB", "debugId": null}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Code/oneoptimizer/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { AlgorithmLibrary } from '@/components/algorithm-library';\nimport { AnalysisWorkspace } from '@/components/analysis-workspace';\nimport { Dashboard } from '@/components/dashboard';\nimport { Navigation } from '@/components/navigation';\nimport { apiClient } from '@/lib/api';\nimport type { AlgorithmsResponse } from '@/lib/api';\n\nexport default function Home() {\n  const [currentView, setCurrentView] = useState<'dashboard' | 'algorithms' | 'workspace'>('dashboard');\n  const [algorithms, setAlgorithms] = useState<AlgorithmsResponse | null>(null);\n  const [selectedAlgorithm, setSelectedAlgorithm] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadAlgorithms();\n  }, []);\n\n  const loadAlgorithms = async () => {\n    try {\n      setIsLoading(true);\n      const data = await apiClient.getAlgorithms();\n      setAlgorithms(data);\n      setError(null);\n    } catch (err) {\n      setError('Failed to load algorithms. Please check if the backend is running.');\n      console.error('Error loading algorithms:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleAlgorithmSelect = (algorithm: any, category: string) => {\n    setSelectedAlgorithm({ ...algorithm, category });\n    setCurrentView('workspace');\n  };\n\n  const handleViewChange = (view: 'dashboard' | 'algorithms' | 'workspace') => {\n    setCurrentView(view);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading OneOptimizer...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto p-6\">\n          <div className=\"text-red-600 text-6xl mb-4\">⚠️</div>\n          <h1 className=\"text-2xl font-bold text-red-800 mb-2\">Connection Error</h1>\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <button\n            onClick={loadAlgorithms}\n            className=\"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\"\n          >\n            Retry Connection\n          </button>\n          <div className=\"mt-4 text-sm text-gray-600\">\n            <p>Make sure the FastAPI backend is running on port 8000:</p>\n            <code className=\"bg-gray-100 px-2 py-1 rounded mt-2 block\">\n              cd backend && python main.py\n            </code>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                📊 OneOptimizer\n              </h1>\n              <span className=\"ml-2 text-sm text-gray-500\">v2.0</span>\n            </div>\n            <Navigation currentView={currentView} onViewChange={handleViewChange} />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {currentView === 'dashboard' && (\n          <Dashboard\n            algorithms={algorithms}\n            onAlgorithmSelect={handleAlgorithmSelect}\n            onViewChange={handleViewChange}\n          />\n        )}\n\n        {currentView === 'algorithms' && algorithms && (\n          <AlgorithmLibrary\n            algorithms={algorithms}\n            onAlgorithmSelect={handleAlgorithmSelect}\n          />\n        )}\n\n        {currentView === 'workspace' && selectedAlgorithm && (\n          <AnalysisWorkspace\n            algorithm={selectedAlgorithm}\n            onBack={() => setCurrentView('algorithms')}\n          />\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,aAAa;YACb,MAAM,OAAO,MAAM,oHAAA,CAAA,YAAS,CAAC,aAAa;YAC1C,cAAc;YACd,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,WAAgB;QAC7C,qBAAqB;YAAE,GAAG,SAAS;YAAE;QAAS;QAC9C,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAG,WAAU;kCAAuC;;;;;;kCACrD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;;;;;;IAOrE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;0CAE/C,6LAAC,mIAAA,CAAA,aAAU;gCAAC,aAAa;gCAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;0BAM1D,6LAAC;gBAAK,WAAU;;oBACb,gBAAgB,6BACf,6LAAC,kIAAA,CAAA,YAAS;wBACR,YAAY;wBACZ,mBAAmB;wBACnB,cAAc;;;;;;oBAIjB,gBAAgB,gBAAgB,4BAC/B,6LAAC,6IAAA,CAAA,mBAAgB;wBACf,YAAY;wBACZ,mBAAmB;;;;;;oBAItB,gBAAgB,eAAe,mCAC9B,6LAAC,8IAAA,CAAA,oBAAiB;wBAChB,WAAW;wBACX,QAAQ,IAAM,eAAe;;;;;;;;;;;;;;;;;;AAMzC;GAhHwB;KAAA", "debugId": null}}]}