import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format numbers for display
export function formatNumber(value: number, decimals: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

// Format currency
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(value);
}

// Format percentage
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${formatNumber(value * 100, decimals)}%`;
}

// Get algorithm icon based on type
export function getAlgorithmIcon(algorithmType: string): string {
  const iconMap: { [key: string]: string } = {
    'time_series_analyzer': '📈',
    'forecasting_engine': '🔮',
    'financial_ratios': '💰',
    'revenue_agent': '🤖',
    'scenario_agent': '🎯',
    'consolidation_agent': '📊',
    'capex_agent': '🏗️',
    'web_research_agent': '🔍',
    'macro_data_agent': '🌍',
  };
  
  return iconMap[algorithmType] || '⚙️';
}

// Get algorithm color based on category
export function getAlgorithmColor(category: string): string {
  const colorMap: { [key: string]: string } = {
    'econometric_models': 'bg-blue-100 text-blue-800 border-blue-200',
    'financial_analysis': 'bg-green-100 text-green-800 border-green-200',
    'ai_agents': 'bg-purple-100 text-purple-800 border-purple-200',
    'risk_analysis': 'bg-red-100 text-red-800 border-red-200',
    'bayesian_models': 'bg-indigo-100 text-indigo-800 border-indigo-200',
  };
  
  return colorMap[category] || 'bg-gray-100 text-gray-800 border-gray-200';
}

// Debounce function for search
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Generate sample data for demos
export function generateSampleTimeSeriesData(points: number = 24): number[] {
  const data: number[] = [];
  let value = 1000000; // Start with 1M
  
  for (let i = 0; i < points; i++) {
    // Add trend + seasonality + noise
    const trend = i * 50000; // Growing trend
    const seasonality = Math.sin((i / 12) * 2 * Math.PI) * 100000; // Annual cycle
    const noise = (Math.random() - 0.5) * 200000; // Random variation
    
    value = 1000000 + trend + seasonality + noise;
    data.push(Math.max(0, value)); // Ensure non-negative
  }
  
  return data;
}

// Generate sample financial data
export function generateSampleFinancialData() {
  return {
    revenue: 50000000,
    cost_of_goods_sold: 30000000,
    operating_expenses: 15000000,
    total_assets: 100000000,
    total_liabilities: 60000000,
    equity: 40000000,
    cash: 10000000,
    debt: 25000000,
    customers: 1000000,
    arpu: 50,
    churn_rate: 0.05,
  };
}

// Validate data for algorithms
export function validateTimeSeriesData(data: any[]): { valid: boolean; message: string } {
  if (!Array.isArray(data)) {
    return { valid: false, message: 'Data must be an array' };
  }
  
  if (data.length < 6) {
    return { valid: false, message: 'Need at least 6 data points for analysis' };
  }
  
  const numericData = data.filter(d => typeof d === 'number' && !isNaN(d));
  if (numericData.length < data.length * 0.8) {
    return { valid: false, message: 'At least 80% of data points must be numeric' };
  }
  
  return { valid: true, message: 'Data is valid' };
}

// Extract business insights from analysis results
export function extractBusinessInsights(analysisResult: any): string[] {
  const insights: string[] = [];
  
  if (analysisResult?.business_insights) {
    analysisResult.business_insights.forEach((insight: any) => {
      if (insight.finding) {
        insights.push(insight.finding);
      }
    });
  }
  
  if (analysisResult?.summary?.key_finding) {
    insights.push(analysisResult.summary.key_finding);
  }
  
  if (analysisResult?.recommendations) {
    analysisResult.recommendations.forEach((rec: any) => {
      if (typeof rec === 'string') {
        insights.push(rec);
      } else if (rec.recommendation) {
        insights.push(rec.recommendation);
      }
    });
  }
  
  return insights;
}
