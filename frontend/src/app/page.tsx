'use client';

import { useState, useEffect } from 'react';
import { AlgorithmLibrary } from '@/components/algorithm-library';
import { AnalysisWorkspace } from '@/components/analysis-workspace';
import { Dashboard } from '@/components/dashboard';
import { DataManagement } from '@/components/data-management';
import { ScenarioPlanning } from '@/components/scenario-planning';
import { CapexOptimization } from '@/components/capex-optimization';
import { StreamNavigation } from '@/components/stream-navigation';
import { apiClient } from '@/lib/api';
import type { AlgorithmsResponse } from '@/lib/api';

export default function Home() {
  const [currentStream, setCurrentStream] = useState<'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights'>('dashboard');
  const [currentView, setCurrentView] = useState<'dashboard' | 'algorithms' | 'workspace' | 'data'>('dashboard');
  const [algorithms, setAlgorithms] = useState<AlgorithmsResponse | null>(null);
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAlgorithms();
  }, []);

  const loadAlgorithms = async () => {
    try {
      setIsLoading(true);
      console.log('Loading algorithms from API...');
      const data = await apiClient.getAlgorithms();
      console.log('Algorithms loaded:', data);
      setAlgorithms(data);
      setError(null);
    } catch (err) {
      setError('Failed to load algorithms. Please check if the backend is running.');
      console.error('Error loading algorithms:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAlgorithmSelect = (algorithm: any, category: string) => {
    console.log('Algorithm selected:', algorithm, 'Category:', category);
    setSelectedAlgorithm({ ...algorithm, category });
    setCurrentView('workspace');
    console.log('View changed to workspace');
  };

  const handleViewChange = (view: 'dashboard' | 'algorithms' | 'workspace' | 'data') => {
    setCurrentView(view);
  };

  const handleStreamChange = (stream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights') => {
    setCurrentStream(stream);
    // Map streams to legacy views for backward compatibility
    if (stream === 'data') {
      setCurrentView('data');
    } else {
      setCurrentView('dashboard');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading OneOptimizer...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Connection Error</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadAlgorithms}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry Connection
          </button>
          <div className="mt-4 text-sm text-gray-600">
            <p>Make sure the FastAPI backend is running on port 8000:</p>
            <code className="bg-gray-100 px-2 py-1 rounded mt-2 block">
              cd backend && python main.py
            </code>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                📊 OneOptimizer
              </h1>
              <span className="ml-2 text-sm text-gray-500">v2.0 - Stream-Based UX</span>
            </div>
          </div>
        </div>
      </header>

      {/* Stream Navigation */}
      <StreamNavigation currentStream={currentStream} onStreamChange={handleStreamChange} />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Debug info */}
        <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
          Current Stream: {currentStream} | Current View: {currentView} | Selected Algorithm: {selectedAlgorithm ? selectedAlgorithm.name : 'None'}
        </div>

        {/* Stream-based Content */}
        {currentStream === 'dashboard' && (
          <Dashboard
            algorithms={algorithms}
            onAlgorithmSelect={handleAlgorithmSelect}
            onViewChange={handleViewChange}
          />
        )}

        {currentStream === 'data' && (
          <DataManagement
            onDataUpdate={(data) => console.log('Data updated:', data)}
          />
        )}

        {currentStream === 'scenarios' && (
          <ScenarioPlanning
            onScenarioUpdate={(scenario) => console.log('Scenario updated:', scenario)}
          />
        )}

        {currentStream === 'capex' && (
          <CapexOptimization
            onOptimizationUpdate={(data) => console.log('CAPEX optimization updated:', data)}
          />
        )}

        {currentStream === 'revenue' && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <div className="text-6xl mb-4">📈</div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Revenue Forecasting</h3>
            <p className="text-gray-600 mb-6">
              Stream 4: Revenue Prediction with scenario-based forecasting, drill-down analysis, and driver attribution.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800 text-sm">
                🚧 <strong>Coming Soon:</strong> Advanced revenue forecasting with macro integration and scenario comparison.
              </p>
            </div>
          </div>
        )}

        {currentStream === 'insights' && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Insights & Reports</h3>
            <p className="text-gray-600 mb-6">
              Stream 5: Natural language insights, executive reports, and customizable dashboards.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800 text-sm">
                🚧 <strong>Coming Soon:</strong> AI-generated insights, PDF reports, and audience-specific dashboards.
              </p>
            </div>
          </div>
        )}

        {/* Legacy Algorithm Views (for backward compatibility) */}
        {currentView === 'algorithms' && algorithms && (
          <AlgorithmLibrary
            algorithms={algorithms}
            onAlgorithmSelect={handleAlgorithmSelect}
          />
        )}

        {currentView === 'workspace' && selectedAlgorithm && (
          <AnalysisWorkspace
            algorithm={selectedAlgorithm}
            onBack={() => setCurrentView('algorithms')}
          />
        )}

        {currentView === 'workspace' && !selectedAlgorithm && (
          <div className="bg-white rounded-lg shadow-sm p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Algorithm Selected</h3>
            <p className="text-gray-600 mb-4">Please select an algorithm from the library first.</p>
            <button
              onClick={() => setCurrentView('algorithms')}
              className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Go to Algorithm Library
            </button>
          </div>
        )}
      </main>
    </div>
  );
}
