'use client';

import { useState, useEffect } from 'react';
import { AlgorithmLibrary } from '@/components/algorithm-library';
import { AnalysisWorkspace } from '@/components/analysis-workspace';
import { Dashboard } from '@/components/dashboard';
import { Navigation } from '@/components/navigation';
import { apiClient } from '@/lib/api';
import type { AlgorithmsResponse } from '@/lib/api';

export default function Home() {
  const [currentView, setCurrentView] = useState<'dashboard' | 'algorithms' | 'workspace'>('dashboard');
  const [algorithms, setAlgorithms] = useState<AlgorithmsResponse | null>(null);
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAlgorithms();
  }, []);

  const loadAlgorithms = async () => {
    try {
      setIsLoading(true);
      const data = await apiClient.getAlgorithms();
      setAlgorithms(data);
      setError(null);
    } catch (err) {
      setError('Failed to load algorithms. Please check if the backend is running.');
      console.error('Error loading algorithms:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAlgorithmSelect = (algorithm: any, category: string) => {
    setSelectedAlgorithm({ ...algorithm, category });
    setCurrentView('workspace');
  };

  const handleViewChange = (view: 'dashboard' | 'algorithms' | 'workspace') => {
    setCurrentView(view);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading OneOptimizer...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Connection Error</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={loadAlgorithms}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry Connection
          </button>
          <div className="mt-4 text-sm text-gray-600">
            <p>Make sure the FastAPI backend is running on port 8000:</p>
            <code className="bg-gray-100 px-2 py-1 rounded mt-2 block">
              cd backend && python main.py
            </code>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                📊 OneOptimizer
              </h1>
              <span className="ml-2 text-sm text-gray-500">v2.0</span>
            </div>
            <Navigation currentView={currentView} onViewChange={handleViewChange} />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'dashboard' && (
          <Dashboard
            algorithms={algorithms}
            onAlgorithmSelect={handleAlgorithmSelect}
            onViewChange={handleViewChange}
          />
        )}

        {currentView === 'algorithms' && algorithms && (
          <AlgorithmLibrary
            algorithms={algorithms}
            onAlgorithmSelect={handleAlgorithmSelect}
          />
        )}

        {currentView === 'workspace' && selectedAlgorithm && (
          <AnalysisWorkspace
            algorithm={selectedAlgorithm}
            onBack={() => setCurrentView('algorithms')}
          />
        )}
      </main>
    </div>
  );
}
