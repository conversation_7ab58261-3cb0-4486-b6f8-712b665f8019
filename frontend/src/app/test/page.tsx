'use client';

import { useState } from 'react';
import { AnalysisWorkspace } from '@/components/analysis-workspace';

export default function TestPage() {
  const [showWorkspace, setShowWorkspace] = useState(false);

  const testAlgorithm = {
    key: 'time_series_analyzer',
    name: 'Time Series Analyzer',
    description: 'ARIMA, seasonal decomposition, trend analysis',
    capabilities: ['trend_analysis', 'seasonality_detection', 'business_insights'],
    input_requirements: ['time_series_data'],
    output_format: 'business_insights_with_recommendations',
    category: 'econometric_models'
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Component Test Page</h1>
        
        {!showWorkspace ? (
          <div className="bg-white rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4">Test Analysis Workspace</h2>
            <p className="text-gray-600 mb-4">
              Click the button below to test the Analysis Workspace component with sample data.
            </p>
            <button
              onClick={() => setShowWorkspace(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Show Analysis Workspace
            </button>
          </div>
        ) : (
          <AnalysisWorkspace
            algorithm={testAlgorithm}
            onBack={() => setShowWorkspace(false)}
          />
        )}
      </div>
    </div>
  );
}
