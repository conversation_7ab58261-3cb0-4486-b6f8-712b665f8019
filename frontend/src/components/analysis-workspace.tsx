'use client';

import { useState } from 'react';
import { apiClient } from '@/lib/api';
import { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData, generateSampleFinancialData, validateTimeSeriesData, extractBusinessInsights } from '@/lib/utils';

interface AnalysisWorkspaceProps {
  algorithm: any;
  onBack: () => void;
}

export function AnalysisWorkspace({ algorithm, onBack }: AnalysisWorkspaceProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [parameters, setParameters] = useState<any>({});

  const handleRunAnalysis = async () => {
    try {
      setIsRunning(true);
      setError(null);
      
      let response;
      
      // Generate appropriate sample data based on algorithm type
      if (algorithm.key === 'time_series_analyzer') {
        const sampleData = generateSampleTimeSeriesData(24);
        const validation = validateTimeSeriesData(sampleData);
        
        if (!validation.valid) {
          throw new Error(validation.message);
        }
        
        response = await apiClient.analyzeTimeSeries({
          data: sampleData,
          context: parameters.context || 'revenue',
        });
      } else if (algorithm.key === 'forecasting_engine') {
        const sampleData = generateSampleTimeSeriesData(24);
        response = await apiClient.createForecast({
          data: sampleData,
          periods: parameters.periods || 12,
          method: parameters.method || 'econometric',
          context: parameters.context || 'revenue',
        });
      } else if (algorithm.key === 'financial_ratios') {
        const sampleData = generateSampleFinancialData();
        response = await apiClient.analyzeFinancialRatios({
          financial_data: sampleData,
          industry_type: parameters.industry_type || 'telecom',
        });
      } else if (algorithm.key === 'revenue_agent') {
        response = await apiClient.runRevenueAgent({
          business_units: parameters.business_units || ['Virgin Mobile Chile', 'Virgin Mobile Colombia'],
          forecast_periods: parameters.periods || 12,
          scenarios: parameters.scenarios || ['Base', 'Optimistic', 'Pessimistic'],
        });
      } else if (algorithm.key === 'scenario_agent') {
        response = await apiClient.runScenarioAgent({
          base_assumptions: parameters.base_assumptions || {
            revenue_growth: 0.15,
            cost_inflation: 0.05,
            market_expansion: 0.10
          },
          risk_parameters: parameters.risk_parameters || {
            economic_volatility: 0.2,
            competitive_pressure: 0.15,
            regulatory_risk: 0.1
          },
          simulation_count: parameters.simulation_count || 1000,
        });
      } else {
        // Generic algorithm execution
        response = await apiClient.runWizardProcess({
          process_type: 'comprehensive',
          algorithm: algorithm.key,
          parameters: parameters,
        });
      }
      
      setResults(response);
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Analysis failed');
      console.error('Analysis error:', err);
    } finally {
      setIsRunning(false);
    }
  };

  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  };

  const renderParameterControls = () => {
    const controls = [];
    
    // Common parameters based on algorithm type
    if (algorithm.input_requirements.includes('time_series_data')) {
      controls.push(
        <div key="context" className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Business Context
          </label>
          <select
            value={parameters.context || 'revenue'}
            onChange={(e) => handleParameterChange('context', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="revenue">Revenue</option>
            <option value="costs">Costs</option>
            <option value="customers">Customers</option>
            <option value="arpu">ARPU</option>
          </select>
        </div>
      );
    }
    
    if (algorithm.input_requirements.includes('forecast_periods')) {
      controls.push(
        <div key="periods" className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Forecast Periods
          </label>
          <input
            type="number"
            min="1"
            max="60"
            value={parameters.periods || 12}
            onChange={(e) => handleParameterChange('periods', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      );
    }
    
    if (algorithm.key === 'forecasting_engine') {
      controls.push(
        <div key="method" className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Forecasting Method
          </label>
          <select
            value={parameters.method || 'econometric'}
            onChange={(e) => handleParameterChange('method', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="econometric">Econometric</option>
            <option value="ai">AI-Powered</option>
            <option value="hybrid">Hybrid</option>
          </select>
        </div>
      );
    }
    
    if (algorithm.key === 'financial_ratios') {
      controls.push(
        <div key="industry" className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Industry Type
          </label>
          <select
            value={parameters.industry_type || 'telecom'}
            onChange={(e) => handleParameterChange('industry_type', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="telecom">Telecommunications</option>
            <option value="fintech">Fintech</option>
            <option value="general">General</option>
          </select>
        </div>
      );
    }
    
    return controls;
  };

  const renderResults = () => {
    if (!results) return null;
    
    const insights = extractBusinessInsights(results.analysis || results.data || results);
    
    return (
      <div className="space-y-6">
        {/* Success Message */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-green-600 text-xl mr-3">✅</div>
            <div>
              <h4 className="font-medium text-green-800">Analysis Complete</h4>
              <p className="text-green-700 text-sm">
                {algorithm.name} executed successfully at {new Date(results.timestamp).toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
        
        {/* Business Insights */}
        {insights.length > 0 && (
          <div className="bg-white rounded-lg border p-6">
            <h4 className="font-semibold text-gray-900 mb-4">💡 Key Insights</h4>
            <div className="space-y-3">
              {insights.map((insight, index) => (
                <div key={index} className="flex items-start">
                  <div className="text-blue-600 mr-3 mt-1">•</div>
                  <p className="text-gray-700">{insight}</p>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Raw Results */}
        <div className="bg-white rounded-lg border p-6">
          <h4 className="font-semibold text-gray-900 mb-4">📊 Detailed Results</h4>
          <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-sm text-gray-700">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={onBack}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              ← Back
            </button>
            <div className="text-3xl mr-4">{getAlgorithmIcon(algorithm.key)}</div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{algorithm.name}</h2>
              <p className="text-gray-600">{algorithm.description}</p>
            </div>
          </div>
          <span className={`px-3 py-1 text-sm font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>
            {algorithm.category.replace('_', ' ')}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">⚙️ Configuration</h3>
            
            {/* Algorithm Info */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-2">CAPABILITIES</h4>
              <div className="space-y-1">
                {algorithm.capabilities.map((capability: string) => (
                  <div key={capability} className="text-sm text-gray-600">
                    • {capability.replace('_', ' ')}
                  </div>
                ))}
              </div>
            </div>
            
            {/* Parameter Controls */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">PARAMETERS</h4>
              {renderParameterControls()}
            </div>
            
            {/* Sample Data Info */}
            <div className="mb-6 p-3 bg-blue-50 rounded-lg">
              <h4 className="text-sm font-medium text-blue-800 mb-1">📊 Sample Data</h4>
              <p className="text-xs text-blue-700">
                This demo uses generated sample data. In production, you would upload your own data files.
              </p>
            </div>
            
            {/* Run Button */}
            <button
              onClick={handleRunAnalysis}
              disabled={isRunning}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isRunning
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-indigo-600 text-white hover:bg-indigo-700'
              }`}
            >
              {isRunning ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Running Analysis...
                </div>
              ) : (
                '🚀 Run Analysis'
              )}
            </button>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📈 Results</h3>
            
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <div className="text-red-600 text-xl mr-3">❌</div>
                  <div>
                    <h4 className="font-medium text-red-800">Analysis Failed</h4>
                    <p className="text-red-700 text-sm">{error}</p>
                  </div>
                </div>
              </div>
            )}
            
            {!results && !error && !isRunning && (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📊</div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Ready to Analyze</h4>
                <p className="text-gray-600">
                  Configure your parameters and click "Run Analysis" to see results.
                </p>
              </div>
            )}
            
            {isRunning && (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Running Analysis</h4>
                <p className="text-gray-600">
                  {algorithm.name} is processing your data...
                </p>
              </div>
            )}
            
            {results && renderResults()}
          </div>
        </div>
      </div>
    </div>
  );
}
