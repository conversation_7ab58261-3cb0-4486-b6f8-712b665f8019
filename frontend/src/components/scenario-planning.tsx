'use client';

import { useState } from 'react';
import { apiClient } from '@/lib/api';

interface ScenarioPlanningProps {
  onScenarioUpdate?: (scenario: any) => void;
}

export function ScenarioPlanning({ onScenarioUpdate }: ScenarioPlanningProps) {
  const [currentStep, setCurrentStep] = useState<'constraints' | 'targets' | 'risk' | 'simulation' | 'results'>('constraints');
  const [constraints, setConstraints] = useState({
    totalOpexLimit: 50000000,
    totalCapexLimit: 25000000,
    minCashReserve: 5000000,
  });
  const [targets, setTargets] = useState({
    revenueGrowth: 0.05,
    marketExpansion: 0.10,
    costReduction: 0.03,
  });
  const [riskAppetite, setRiskAppetite] = useState<'low' | 'medium' | 'high'>('medium');
  const [simulationResults, setSimulationResults] = useState<any>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [savedScenarios, setSavedScenarios] = useState<any[]>([]);

  const steps = [
    { id: 'constraints', label: 'Define Constraints', icon: '⚙️', completed: true },
    { id: 'targets', label: 'Set Growth Targets', icon: '🎯', completed: currentStep !== 'constraints' },
    { id: 'risk', label: 'Risk Appetite', icon: '⚖️', completed: ['simulation', 'results'].includes(currentStep) },
    { id: 'simulation', label: 'Run Simulation', icon: '🎲', completed: currentStep === 'results' },
    { id: 'results', label: 'Explore Results', icon: '📊', completed: false },
  ];

  const runSimulation = async () => {
    try {
      setIsRunning(true);
      
      const response = await apiClient.runScenarioAgent({
        base_assumptions: {
          revenue_growth: targets.revenueGrowth,
          market_expansion: targets.marketExpansion,
          cost_reduction: targets.costReduction,
        },
        constraints: {
          opex_limit: constraints.totalOpexLimit,
          capex_limit: constraints.totalCapexLimit,
          cash_reserve: constraints.minCashReserve,
        },
        risk_parameters: {
          risk_appetite: riskAppetite,
          economic_volatility: riskAppetite === 'low' ? 0.1 : riskAppetite === 'medium' ? 0.2 : 0.3,
          market_uncertainty: 0.15,
        },
        simulation_count: 1000,
      });

      if (response.success) {
        setSimulationResults(response.data);
        setCurrentStep('results');
        onScenarioUpdate?.(response.data);
      }
    } catch (error) {
      console.error('Simulation error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const saveScenario = () => {
    const scenario = {
      id: Date.now(),
      name: `Scenario ${savedScenarios.length + 1}`,
      constraints,
      targets,
      riskAppetite,
      results: simulationResults,
      createdAt: new Date().toISOString(),
    };
    setSavedScenarios([...savedScenarios, scenario]);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'constraints':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Define Financial Constraints</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total OPEX Limit
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={constraints.totalOpexLimit}
                    onChange={(e) => setConstraints({...constraints, totalOpexLimit: Number(e.target.value)})}
                    className="pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Total CAPEX Limit
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={constraints.totalCapexLimit}
                    onChange={(e) => setConstraints({...constraints, totalCapexLimit: Number(e.target.value)})}
                    className="pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Cash Reserve
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={constraints.minCashReserve}
                    onChange={(e) => setConstraints({...constraints, minCashReserve: Number(e.target.value)})}
                    className="pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
            </div>

            <button
              onClick={() => setCurrentStep('targets')}
              className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Next: Set Growth Targets →
            </button>
          </div>
        );

      case 'targets':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Set Growth Targets</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Revenue Growth Target
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={targets.revenueGrowth}
                    onChange={(e) => setTargets({...targets, revenueGrowth: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                  <span className="absolute right-3 top-2 text-gray-500">%</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{(targets.revenueGrowth * 100).toFixed(1)}% annual growth</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Market Expansion
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={targets.marketExpansion}
                    onChange={(e) => setTargets({...targets, marketExpansion: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                  <span className="absolute right-3 top-2 text-gray-500">%</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{(targets.marketExpansion * 100).toFixed(1)}% market growth</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cost Reduction Target
                </label>
                <div className="relative">
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={targets.costReduction}
                    onChange={(e) => setTargets({...targets, costReduction: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                  <span className="absolute right-3 top-2 text-gray-500">%</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{(targets.costReduction * 100).toFixed(1)}% cost reduction</p>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setCurrentStep('constraints')}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={() => setCurrentStep('risk')}
                className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Next: Risk Appetite →
              </button>
            </div>
          </div>
        );

      case 'risk':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Select Risk Appetite</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {(['low', 'medium', 'high'] as const).map((level) => (
                <div
                  key={level}
                  className={`p-6 border-2 rounded-lg cursor-pointer transition-colors ${
                    riskAppetite === level
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setRiskAppetite(level)}
                >
                  <div className="text-center">
                    <div className="text-3xl mb-2">
                      {level === 'low' ? '🛡️' : level === 'medium' ? '⚖️' : '🚀'}
                    </div>
                    <h4 className="font-semibold text-gray-900 capitalize">{level} Risk</h4>
                    <p className="text-sm text-gray-600 mt-2">
                      {level === 'low' && 'Conservative approach, prioritize stability'}
                      {level === 'medium' && 'Balanced risk-reward optimization'}
                      {level === 'high' && 'Aggressive growth, higher volatility'}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setCurrentStep('targets')}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={() => setCurrentStep('simulation')}
                className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Next: Run Simulation →
              </button>
            </div>
          </div>
        );

      case 'simulation':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Run Monte Carlo Simulation</h3>
            
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-medium text-gray-900 mb-4">Simulation Parameters</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">OPEX Limit:</span>
                  <div>${constraints.totalOpexLimit.toLocaleString()}</div>
                </div>
                <div>
                  <span className="font-medium">CAPEX Limit:</span>
                  <div>${constraints.totalCapexLimit.toLocaleString()}</div>
                </div>
                <div>
                  <span className="font-medium">Revenue Growth:</span>
                  <div>{(targets.revenueGrowth * 100).toFixed(1)}%</div>
                </div>
                <div>
                  <span className="font-medium">Risk Level:</span>
                  <div className="capitalize">{riskAppetite}</div>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setCurrentStep('risk')}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={runSimulation}
                disabled={isRunning}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
              >
                {isRunning ? '🎲 Running Simulation...' : '🎲 Run Monte Carlo Simulation'}
              </button>
            </div>
          </div>
        );

      case 'results':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Simulation Results</h3>
              <button
                onClick={saveScenario}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                💾 Save Scenario
              </button>
            </div>
            
            {simulationResults && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white border rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-2">📊 Probability Distribution</h4>
                  <p className="text-sm text-gray-600">Monte Carlo results with confidence intervals</p>
                  {/* Placeholder for probability chart */}
                  <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                    <span className="text-gray-500">Probability Chart</span>
                  </div>
                </div>

                <div className="bg-white border rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-2">🔥 Risk Heatmap</h4>
                  <p className="text-sm text-gray-600">Risk levels by domain/country</p>
                  <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                    <span className="text-gray-500">Risk Heatmap</span>
                  </div>
                </div>

                <div className="bg-white border rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-2">🎚️ Sensitivity Analysis</h4>
                  <p className="text-sm text-gray-600">Impact of parameter changes</p>
                  <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                    <span className="text-gray-500">Sensitivity Sliders</span>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm text-gray-700 overflow-auto max-h-64">
                {JSON.stringify(simulationResults, null, 2)}
              </pre>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">🎯 Scenario Planning</h2>
        <p className="text-gray-600">
          Define constraints, set growth targets, and run Monte Carlo simulations to explore possible outcomes.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer
                  ${step.completed || currentStep === step.id
                    ? 'bg-indigo-600 border-indigo-600 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                  }
                `}
                onClick={() => setCurrentStep(step.id as any)}
              >
                {step.completed ? '✓' : step.icon}
              </div>
              <div className="ml-3 hidden md:block">
                <div className={`text-sm font-medium ${
                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {renderStepContent()}
      </div>

      {/* Saved Scenarios */}
      {savedScenarios.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💾 Saved Scenarios ({savedScenarios.length})</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {savedScenarios.map((scenario) => (
              <div key={scenario.id} className="border rounded-lg p-4">
                <h4 className="font-medium text-gray-900">{scenario.name}</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Risk: {scenario.riskAppetite} | Growth: {(scenario.targets.revenueGrowth * 100).toFixed(1)}%
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  {new Date(scenario.createdAt).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
