'use client';

import { useState } from 'react';
import type { AlgorithmsResponse } from '@/lib/api';
import { getAlgorithmIcon, getAlgorithmColor, debounce } from '@/lib/utils';

interface AlgorithmLibraryProps {
  algorithms: AlgorithmsResponse;
  onAlgorithmSelect: (algorithm: any, category: string) => void;
}

export function AlgorithmLibrary({ algorithms, onAlgorithmSelect }: AlgorithmLibraryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'category'>('category');

  // Debounced search
  const debouncedSearch = debounce((term: string) => {
    setSearchTerm(term);
  }, 300);

  // Flatten algorithms for filtering and sorting
  const allAlgorithms = [];
  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {
    for (const [algorithmKey, algorithm] of Object.entries(categoryAlgorithms)) {
      allAlgorithms.push({
        key: algorithm<PERSON>ey,
        category: categoryName,
        ...algorithm,
      });
    }
  }

  // Filter algorithms
  const filteredAlgorithms = allAlgorithms.filter((algorithm) => {
    const matchesSearch = searchTerm === '' || 
      algorithm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      algorithm.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      algorithm.capabilities.some((cap: string) => 
        cap.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesCategory = selectedCategory === 'all' || algorithm.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort algorithms
  const sortedAlgorithms = [...filteredAlgorithms].sort((a, b) => {
    if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    } else {
      return a.category.localeCompare(b.category);
    }
  });

  const categories = ['all', ...algorithms.categories];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Algorithm Library</h2>
        <p className="text-gray-600">
          Discover and explore {algorithms.total_count} advanced algorithms for financial analysis and forecasting.
        </p>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
              Search Algorithms
            </label>
            <input
              type="text"
              id="search"
              placeholder="Search by name, description, or capabilities..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              onChange={(e) => debouncedSearch(e.target.value)}
            />
          </div>

          {/* Category Filter */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              id="category"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div>
            <label htmlFor="sort" className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'category')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="category">Category</option>
              <option value="name">Name</option>
            </select>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600">
          Showing {sortedAlgorithms.length} of {algorithms.total_count} algorithms
        </div>
      </div>

      {/* Algorithm Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedAlgorithms.map((algorithm) => (
          <div
            key={`${algorithm.category}-${algorithm.key}`}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="text-3xl">{getAlgorithmIcon(algorithm.key)}</div>
                <span className={`px-3 py-1 text-xs font-medium rounded-full ${getAlgorithmColor(algorithm.category)}`}>
                  {algorithm.category.replace('_', ' ')}
                </span>
              </div>

              {/* Content */}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{algorithm.name}</h3>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{algorithm.description}</p>

              {/* Capabilities */}
              <div className="mb-4">
                <h4 className="text-xs font-medium text-gray-700 mb-2">CAPABILITIES</h4>
                <div className="flex flex-wrap gap-1">
                  {algorithm.capabilities.slice(0, 3).map((capability: string) => (
                    <span
                      key={capability}
                      className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                    >
                      {capability.replace('_', ' ')}
                    </span>
                  ))}
                  {algorithm.capabilities.length > 3 && (
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                      +{algorithm.capabilities.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              {/* Input Requirements */}
              <div className="mb-4">
                <h4 className="text-xs font-medium text-gray-700 mb-2">REQUIRES</h4>
                <div className="flex flex-wrap gap-1">
                  {algorithm.input_requirements.map((requirement: string) => (
                    <span
                      key={requirement}
                      className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded border border-blue-200"
                    >
                      {requirement.replace('_', ' ')}
                    </span>
                  ))}
                </div>
              </div>

              {/* Output Format */}
              <div className="mb-4">
                <h4 className="text-xs font-medium text-gray-700 mb-1">OUTPUT</h4>
                <span className="text-xs text-gray-600">
                  {algorithm.output_format.replace('_', ' ')}
                </span>
              </div>

              {/* Action Button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onAlgorithmSelect(algorithm, algorithm.category);
                }}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors text-sm font-medium"
              >
                Configure & Run
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {sortedAlgorithms.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <div className="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No algorithms found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search terms or category filter.
          </p>
          <button
            onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
            }}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
          >
            Clear Filters
          </button>
        </div>
      )}

      {/* Category Overview */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {algorithms.categories.map((category) => {
            const categoryAlgorithms = Object.keys(algorithms.algorithms[category as keyof typeof algorithms.algorithms]).length;
            return (
              <div
                key={category}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                  selectedCategory === category
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedCategory(category)}
              >
                <div className={`text-sm font-medium mb-1 ${getAlgorithmColor(category).split(' ')[1]}`}>
                  {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </div>
                <div className="text-2xl font-bold text-gray-900">{categoryAlgorithms}</div>
                <div className="text-xs text-gray-600">algorithms</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
