'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface MacroDashboardProps {
  onDataUpdate?: (data: any) => void;
}

export function MacroDashboard({ onDataUpdate }: MacroDashboardProps) {
  const [macroData, setMacroData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCountries, setSelectedCountries] = useState(['US', 'CL', 'CO', 'OM']);
  const [selectedIndicators, setSelectedIndicators] = useState(['GDP', 'INFLATION', 'UNEMPLOYMENT']);
  const [availableCountries, setAvailableCountries] = useState<string[]>([]);
  const [availableIndicators, setAvailableIndicators] = useState<string[]>([]);

  useEffect(() => {
    loadAvailableOptions();
  }, []);

  const loadAvailableOptions = async () => {
    try {
      const [countriesResponse, indicatorsResponse] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/countries`),
        fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/indicators/list`)
      ]);

      if (countriesResponse.ok) {
        const countriesData = await countriesResponse.json();
        setAvailableCountries(countriesData.countries || []);
      }

      if (indicatorsResponse.ok) {
        const indicatorsData = await indicatorsResponse.json();
        setAvailableIndicators(indicatorsData.indicators || []);
      }
    } catch (err) {
      console.error('Error loading options:', err);
    }
  };

  const loadMacroData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        countries: selectedCountries.join(','),
        indicators: selectedIndicators.join(','),
        years: '2022,2023,2024'
      });

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/indicators?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch macro data: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setMacroData(data);
        onDataUpdate?.(data);
      } else {
        throw new Error(data.message || 'Failed to load macro data');
      }

    } catch (err: any) {
      setError(err.message);
      console.error('Macro data error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const runMacroAnalysis = async () => {
    try {
      setIsLoading(true);

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/macro/analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          countries: selectedCountries,
          business_context: 'telecom',
          analysis_type: 'comprehensive'
        }),
      });

      if (!response.ok) {
        throw new Error(`Analysis failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setMacroData(data);
        onDataUpdate?.(data);
      } else {
        throw new Error(data.message || 'Analysis failed');
      }

    } catch (err: any) {
      setError(err.message);
      console.error('Macro analysis error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const countryFlags: { [key: string]: string } = {
    'US': '🇺🇸',
    'CL': '🇨🇱',
    'CO': '🇨🇴',
    'OM': '🇴🇲',
    'GB': '🇬🇧',
    'DE': '🇩🇪',
    'FR': '🇫🇷',
    'JP': '🇯🇵',
    'CN': '🇨🇳',
  };

  const indicatorIcons: { [key: string]: string } = {
    'GDP': '📈',
    'INFLATION': '💰',
    'UNEMPLOYMENT': '👥',
    'INTEREST_RATE': '🏦',
    'EXCHANGE_RATE': '💱',
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">🌍 Macro Economics Dashboard</h2>
        <p className="text-gray-600">
          Monitor key economic indicators for your business markets and analyze their impact.
        </p>
      </div>

      {/* Configuration */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">⚙️ Configuration</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Countries Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Countries
            </label>
            <div className="space-y-2">
              {availableCountries.slice(0, 8).map((country) => (
                <label key={country} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedCountries.includes(country)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedCountries([...selectedCountries, country]);
                      } else {
                        setSelectedCountries(selectedCountries.filter(c => c !== country));
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="mr-2">{countryFlags[country] || '🌍'}</span>
                  <span>{country}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Indicators Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Economic Indicators
            </label>
            <div className="space-y-2">
              {availableIndicators.slice(0, 6).map((indicator) => (
                <label key={indicator} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedIndicators.includes(indicator)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedIndicators([...selectedIndicators, indicator]);
                      } else {
                        setSelectedIndicators(selectedIndicators.filter(i => i !== indicator));
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="mr-2">{indicatorIcons[indicator] || '📊'}</span>
                  <span>{indicator.replace('_', ' ')}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 flex gap-4">
          <button
            onClick={loadMacroData}
            disabled={isLoading || selectedCountries.length === 0 || selectedIndicators.length === 0}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
          >
            {isLoading ? 'Loading...' : '📊 Load Data'}
          </button>
          
          <button
            onClick={runMacroAnalysis}
            disabled={isLoading || selectedCountries.length === 0}
            className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300 transition-colors"
          >
            {isLoading ? 'Analyzing...' : '🔍 Run Analysis'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-3">❌</span>
            <div>
              <h4 className="font-medium text-red-800">Error Loading Macro Data</h4>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">Loading Macro Economic Data</h4>
          <p className="text-gray-600">Fetching data from external sources...</p>
        </div>
      )}

      {/* Results Display */}
      {macroData && !isLoading && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-3">🌍</span>
                <h4 className="font-semibold text-gray-900">Countries</h4>
              </div>
              <div className="text-2xl font-bold text-blue-600">{macroData.countries?.length || 0}</div>
              <div className="text-sm text-gray-600">Markets analyzed</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-3">📊</span>
                <h4 className="font-semibold text-gray-900">Indicators</h4>
              </div>
              <div className="text-2xl font-bold text-green-600">{macroData.indicators?.length || 0}</div>
              <div className="text-sm text-gray-600">Economic metrics</div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-3">📅</span>
                <h4 className="font-semibold text-gray-900">Time Period</h4>
              </div>
              <div className="text-2xl font-bold text-purple-600">{macroData.years?.length || 0}</div>
              <div className="text-sm text-gray-600">Years of data</div>
            </div>
          </div>

          {/* Business Analysis */}
          {macroData.business_analysis && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">💼 Business Impact Analysis</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Key Insights */}
                {macroData.business_analysis.key_insights && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">💡 Key Insights</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      {macroData.business_analysis.key_insights.map((insight: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          {insight}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Risk Factors */}
                {macroData.business_analysis.risk_factors && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">⚠️ Risk Factors</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      {macroData.business_analysis.risk_factors.map((risk: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="text-red-500 mr-2">•</span>
                          {risk}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Opportunities */}
                {macroData.business_analysis.opportunities && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">🚀 Opportunities</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      {macroData.business_analysis.opportunities.map((opportunity: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="text-green-500 mr-2">•</span>
                          {opportunity}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* Recommendations */}
              {macroData.business_analysis.recommendations && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-3">📋 Strategic Recommendations</h4>
                  <ul className="space-y-2 text-sm text-blue-800">
                    {macroData.business_analysis.recommendations.map((rec: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-600 mr-2">→</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Raw Data Display */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Economic Data</h3>
            <div className="bg-gray-50 rounded-lg p-4 overflow-auto max-h-96">
              <pre className="text-sm text-gray-700">
                {JSON.stringify(macroData.data || macroData.macro_data, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!macroData && !isLoading && !error && (
        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
          <div className="text-gray-400 text-6xl mb-4">🌍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Load Macro Data</h3>
          <p className="text-gray-600 mb-4">
            Select countries and indicators, then click "Load Data" to get started.
          </p>
        </div>
      )}
    </div>
  );
}
