'use client';

interface NavigationProps {
  currentView: 'dashboard' | 'algorithms' | 'workspace';
  onViewChange: (view: 'dashboard' | 'algorithms' | 'workspace') => void;
}

export function Navigation({ currentView, onViewChange }: NavigationProps) {
  const navItems = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'algorithms', label: 'Algorithm Library', icon: '🧮' },
    { id: 'workspace', label: 'Analysis Workspace', icon: '⚙️' },
  ] as const;

  return (
    <nav className="flex space-x-1">
      {navItems.map((item) => (
        <button
          key={item.id}
          onClick={() => onViewChange(item.id)}
          className={`
            flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
            ${currentView === item.id
              ? 'bg-indigo-100 text-indigo-700'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
            }
          `}
        >
          <span className="mr-2">{item.icon}</span>
          {item.label}
        </button>
      ))}
    </nav>
  );
}
