'use client';

import { useState } from 'react';
import type { AlgorithmsResponse } from '@/lib/api';
import { getAlgorithmIcon, getAlgorithmColor, generateSampleTimeSeriesData } from '@/lib/utils';

interface DashboardProps {
  algorithms: AlgorithmsResponse | null;
  onAlgorithmSelect: (algorithm: any, category: string) => void;
  onViewChange: (view: 'dashboard' | 'algorithms' | 'workspace') => void;
}

export function Dashboard({ algorithms, onAlgorithmSelect, onViewChange }: DashboardProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  if (!algorithms) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">Loading algorithms...</div>
      </div>
    );
  }

  const totalAlgorithms = algorithms.total_count;
  const categories = algorithms.categories;

  // Get featured algorithms (first 3 from each category)
  const featuredAlgorithms = [];
  for (const [categoryName, categoryAlgorithms] of Object.entries(algorithms.algorithms)) {
    const algorithmEntries = Object.entries(categoryAlgorithms).slice(0, 2);
    for (const [algorithmKey, algorithm] of algorithmEntries) {
      featuredAlgorithms.push({
        key: algorithmKey,
        category: categoryName,
        ...algorithm,
      });
    }
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to OneOptimizer v2.0
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            AI-Powered Budgeting System with Advanced Analytics
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-4xl mb-2">🧮</div>
              <h3 className="font-semibold text-gray-900">Advanced Algorithms</h3>
              <p className="text-gray-600">Bayesian forecasting, econometric models, and AI agents</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">📊</div>
              <h3 className="font-semibold text-gray-900">Interactive Analysis</h3>
              <p className="text-gray-600">Real-time parameter adjustment and visualization</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">🎯</div>
              <h3 className="font-semibold text-gray-900">Business Insights</h3>
              <p className="text-gray-600">Clear recommendations and actionable insights</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="text-2xl mr-3">🧮</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{totalAlgorithms}</div>
              <div className="text-sm text-gray-600">Available Algorithms</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="text-2xl mr-3">📂</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{categories.length}</div>
              <div className="text-sm text-gray-600">Algorithm Categories</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="text-2xl mr-3">🤖</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">7</div>
              <div className="text-sm text-gray-600">AI Agents</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <div className="text-2xl mr-3">⚡</div>
            <div>
              <div className="text-2xl font-bold text-gray-900">Ready</div>
              <div className="text-sm text-gray-600">System Status</div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => onViewChange('algorithms')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-indigo-400 hover:bg-indigo-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">🔍</div>
            <h4 className="font-medium text-gray-900">Explore Algorithms</h4>
            <p className="text-sm text-gray-600">Browse and discover available analysis algorithms</p>
          </button>
          <button
            onClick={() => {
              // Select a sample algorithm for demo
              const sampleAlgorithm = featuredAlgorithms[0];
              if (sampleAlgorithm) {
                onAlgorithmSelect(sampleAlgorithm, sampleAlgorithm.category);
              }
            }}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">🚀</div>
            <h4 className="font-medium text-gray-900">Quick Analysis</h4>
            <p className="text-sm text-gray-600">Start analyzing with sample data</p>
          </button>
          <button
            onClick={() => {
              // Generate sample data and start analysis
              const sampleData = generateSampleTimeSeriesData();
              console.log('Generated sample data:', sampleData);
            }}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">📊</div>
            <h4 className="font-medium text-gray-900">Sample Data</h4>
            <p className="text-sm text-gray-600">Generate sample data for testing</p>
          </button>
        </div>
      </div>

      {/* Featured Algorithms */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Featured Algorithms</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {featuredAlgorithms.slice(0, 6).map((algorithm) => (
            <div
              key={`${algorithm.category}-${algorithm.key}`}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onAlgorithmSelect(algorithm, algorithm.category)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="text-2xl">{getAlgorithmIcon(algorithm.key)}</div>
                <span className={`px-2 py-1 text-xs rounded-full ${getAlgorithmColor(algorithm.category)}`}>
                  {algorithm.category.replace('_', ' ')}
                </span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">{algorithm.name}</h4>
              <p className="text-sm text-gray-600 mb-3">{algorithm.description}</p>
              <div className="flex flex-wrap gap-1">
                {algorithm.capabilities.slice(0, 2).map((capability: string) => (
                  <span
                    key={capability}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                  >
                    {capability.replace('_', ' ')}
                  </span>
                ))}
                {algorithm.capabilities.length > 2 && (
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                    +{algorithm.capabilities.length - 2} more
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-6 text-center">
          <button
            onClick={() => onViewChange('algorithms')}
            className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            View All Algorithms
          </button>
        </div>
      </div>

      {/* Getting Started */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-sm p-6 text-white">
        <h3 className="text-xl font-semibold mb-4">Getting Started</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">1. Choose Your Algorithm</h4>
            <p className="text-indigo-100 text-sm">
              Browse our library of advanced algorithms including Bayesian forecasting, 
              econometric models, and AI agents.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">2. Configure Parameters</h4>
            <p className="text-indigo-100 text-sm">
              Adjust algorithm parameters in real-time with our interactive 
              configuration interface.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">3. Analyze Results</h4>
            <p className="text-indigo-100 text-sm">
              View interactive visualizations, confidence intervals, and 
              business insights from your analysis.
            </p>
          </div>
          <div>
            <h4 className="font-medium mb-2">4. Export & Share</h4>
            <p className="text-indigo-100 text-sm">
              Export results in multiple formats and share insights with 
              your team.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
