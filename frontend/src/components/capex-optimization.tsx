'use client';

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';

interface CapexOptimizationProps {
  onOptimizationUpdate?: (data: any) => void;
}

export function CapexOptimization({ onOptimizationUpdate }: CapexOptimizationProps) {
  const [currentStep, setCurrentStep] = useState<'upload' | 'scoring' | 'weights' | 'optimize' | 'whatif'>('upload');
  const [capexData, setCapexData] = useState<any[]>([]);
  const [scoredProjects, setScoredProjects] = useState<any[]>([]);
  const [weights, setWeights] = useState({
    strategic_alignment: 0.3,
    roi_potential: 0.4,
    risk_level: 0.2,
    implementation_ease: 0.1,
  });
  const [optimizationGoal, setOptimizationGoal] = useState<'maximize_roi' | 'minimize_risk' | 'balanced'>('balanced');
  const [optimizedAllocation, setOptimizedAllocation] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const steps = [
    { id: 'upload', label: 'Upload CAPEX Data', icon: '📁', completed: capexData.length > 0 },
    { id: 'scoring', label: 'AI Scoring', icon: '🤖', completed: scoredProjects.length > 0 },
    { id: 'weights', label: 'Set Weights', icon: '⚖️', completed: false },
    { id: 'optimize', label: 'Optimize Allocation', icon: '🎯', completed: optimizedAllocation !== null },
    { id: 'whatif', label: 'What-If Analysis', icon: '🔍', completed: false },
  ];

  const runAIScoring = async () => {
    try {
      setIsProcessing(true);
      
      // Simulate AI scoring for demo
      const scored = capexData.map((project, index) => ({
        ...project,
        ai_score: Math.floor(Math.random() * 40) + 60, // 60-100 score
        strategic_alignment: Math.floor(Math.random() * 30) + 70,
        roi_potential: Math.floor(Math.random() * 35) + 65,
        risk_level: Math.floor(Math.random() * 40) + 30, // Lower is better
        implementation_ease: Math.floor(Math.random() * 25) + 75,
        justification: [
          'Strong alignment with digital transformation strategy',
          'Positive ROI based on historical data and market trends',
          'Moderate implementation risk with clear mitigation plan'
        ]
      }));
      
      setScoredProjects(scored);
      setCurrentStep('weights');
      
    } catch (error) {
      console.error('AI scoring error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const runOptimization = async () => {
    try {
      setIsProcessing(true);
      
      // Calculate weighted scores
      const weightedProjects = scoredProjects.map(project => ({
        ...project,
        weighted_score: (
          project.strategic_alignment * weights.strategic_alignment +
          project.roi_potential * weights.roi_potential +
          (100 - project.risk_level) * weights.risk_level + // Invert risk
          project.implementation_ease * weights.implementation_ease
        )
      }));

      // Sort by weighted score
      const optimized = weightedProjects.sort((a, b) => b.weighted_score - a.weighted_score);
      
      setOptimizedAllocation({
        projects: optimized,
        total_budget: optimized.reduce((sum, p) => sum + (p.budget || 0), 0),
        expected_roi: optimized.reduce((sum, p) => sum + (p.roi_potential * (p.budget || 0) / 100), 0),
        risk_score: optimized.reduce((sum, p) => sum + p.risk_level, 0) / optimized.length,
      });
      
      setCurrentStep('whatif');
      onOptimizationUpdate?.(optimized);
      
    } catch (error) {
      console.error('Optimization error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Upload CAPEX Data</h3>
            
            {capexData.length === 0 ? (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div className="text-4xl mb-4">📁</div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">Upload CAPEX Planning Data</h4>
                <p className="text-gray-600 mb-4">
                  Upload your CAPEX planning spreadsheet with project details, budgets, and timelines.
                </p>
                <button
                  onClick={() => {
                    // Simulate data upload for demo
                    const sampleData = [
                      { id: 1, name: '5G Network Expansion', budget: 15000000, category: 'Infrastructure', priority: 'High' },
                      { id: 2, name: 'Digital Platform Upgrade', budget: 8000000, category: 'Technology', priority: 'Medium' },
                      { id: 3, name: 'Customer Experience Center', budget: 3000000, category: 'Customer', priority: 'Low' },
                      { id: 4, name: 'Data Analytics Platform', budget: 5000000, category: 'Analytics', priority: 'High' },
                      { id: 5, name: 'Security Infrastructure', budget: 4000000, category: 'Security', priority: 'Medium' },
                    ];
                    setCapexData(sampleData);
                  }}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  📁 Upload Sample CAPEX Data
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800">✅ CAPEX Data Loaded</h4>
                  <p className="text-green-700 text-sm">
                    {capexData.length} projects loaded with total budget of ${capexData.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}
                  </p>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Project</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Budget</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Priority</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {capexData.map((project) => (
                        <tr key={project.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {project.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${project.budget.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {project.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              project.priority === 'High' ? 'bg-red-100 text-red-800' :
                              project.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {project.priority}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <button
                  onClick={() => setCurrentStep('scoring')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: AI Scoring →
                </button>
              </div>
            )}
          </div>
        );

      case 'scoring':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">AI Scoring & Analysis</h3>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">🤖 AI Scoring Criteria</h4>
              <ul className="text-blue-700 text-sm space-y-1">
                <li>• <strong>Strategic Alignment:</strong> How well the project aligns with business strategy</li>
                <li>• <strong>ROI Potential:</strong> Expected return on investment based on historical data</li>
                <li>• <strong>Risk Level:</strong> Implementation and business risks</li>
                <li>• <strong>Implementation Ease:</strong> Complexity and resource requirements</li>
              </ul>
            </div>

            {scoredProjects.length === 0 ? (
              <div className="text-center py-8">
                <button
                  onClick={runAIScoring}
                  disabled={isProcessing}
                  className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                >
                  {isProcessing ? '🤖 AI Analyzing Projects...' : '🤖 Run AI Scoring'}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800">✅ AI Scoring Complete</h4>
                  <p className="text-green-700 text-sm">
                    All {scoredProjects.length} projects have been scored and analyzed.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {scoredProjects.map((project) => (
                    <div key={project.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <h4 className="font-medium text-gray-900">{project.name}</h4>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-indigo-600">{project.ai_score}</div>
                          <div className="text-xs text-gray-500">AI Score</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                        <div>Strategic: {project.strategic_alignment}/100</div>
                        <div>ROI: {project.roi_potential}/100</div>
                        <div>Risk: {project.risk_level}/100</div>
                        <div>Ease: {project.implementation_ease}/100</div>
                      </div>
                      
                      <div className="text-xs text-gray-600">
                        <strong>Justification:</strong>
                        <ul className="mt-1 space-y-1">
                          {project.justification.map((reason: string, index: number) => (
                            <li key={index}>• {reason}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>

                <button
                  onClick={() => setCurrentStep('weights')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: Set Weights →
                </button>
              </div>
            )}
          </div>
        );

      case 'weights':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Set Strategic Weights</h3>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">⚖️ Weight Configuration</h4>
              <p className="text-yellow-700 text-sm">
                Adjust the importance of each scoring criteria based on your strategic priorities.
              </p>
            </div>

            <div className="space-y-4">
              {Object.entries(weights).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-4">
                  <div className="w-40">
                    <label className="text-sm font-medium text-gray-700 capitalize">
                      {key.replace('_', ' ')}
                    </label>
                  </div>
                  <div className="flex-1">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={value}
                      onChange={(e) => setWeights({...weights, [key]: Number(e.target.value)})}
                      className="w-full"
                    />
                  </div>
                  <div className="w-16 text-right">
                    <span className="text-sm font-medium">{(value * 100).toFixed(0)}%</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Optimization Goal</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { id: 'maximize_roi', label: 'Maximize ROI', icon: '💰', desc: 'Focus on highest return projects' },
                  { id: 'minimize_risk', label: 'Minimize Risk', icon: '🛡️', desc: 'Prioritize low-risk projects' },
                  { id: 'balanced', label: 'Balanced', icon: '⚖️', desc: 'Balance risk and return' },
                ].map((goal) => (
                  <div
                    key={goal.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      optimizationGoal === goal.id
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setOptimizationGoal(goal.id as any)}
                  >
                    <div className="text-center">
                      <div className="text-2xl mb-2">{goal.icon}</div>
                      <h5 className="font-medium text-gray-900">{goal.label}</h5>
                      <p className="text-sm text-gray-600 mt-1">{goal.desc}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-4">
              <button
                onClick={() => setCurrentStep('scoring')}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
              >
                ← Back
              </button>
              <button
                onClick={() => setCurrentStep('optimize')}
                className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Next: Optimize →
              </button>
            </div>
          </div>
        );

      case 'optimize':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Optimize CAPEX Allocation</h3>
            
            {!optimizedAllocation ? (
              <div className="text-center py-8">
                <button
                  onClick={runOptimization}
                  disabled={isProcessing}
                  className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                >
                  {isProcessing ? '🎯 Optimizing Allocation...' : '🎯 Run Optimization'}
                </button>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">💰 Total Budget</h4>
                    <div className="text-2xl font-bold text-green-600">
                      ${optimizedAllocation.total_budget.toLocaleString()}
                    </div>
                  </div>
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">📈 Expected ROI</h4>
                    <div className="text-2xl font-bold text-blue-600">
                      ${optimizedAllocation.expected_roi.toLocaleString()}
                    </div>
                  </div>
                  <div className="bg-white border rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-2">⚠️ Risk Score</h4>
                    <div className="text-2xl font-bold text-orange-600">
                      {optimizedAllocation.risk_score.toFixed(1)}/100
                    </div>
                  </div>
                </div>

                <div className="bg-white border rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">🏆 Prioritized Project List</h4>
                  <div className="space-y-3">
                    {optimizedAllocation.projects.map((project: any, index: number) => (
                      <div key={project.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                            {index + 1}
                          </div>
                          <div>
                            <h5 className="font-medium text-gray-900">{project.name}</h5>
                            <p className="text-sm text-gray-600">
                              Score: {project.weighted_score.toFixed(1)} | Budget: ${project.budget.toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-indigo-600">
                            Priority {index + 1}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <button
                  onClick={() => setCurrentStep('whatif')}
                  className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Next: What-If Analysis →
                </button>
              </div>
            )}
          </div>
        );

      case 'whatif':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">What-If Analysis</h3>
            
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-medium text-purple-800 mb-2">🔍 Interactive Analysis</h4>
              <p className="text-purple-700 text-sm">
                Adjust project allocations and see real-time impact on ROI and risk.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Adjust Allocations</h4>
                {optimizedAllocation?.projects.slice(0, 3).map((project: any) => (
                  <div key={project.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h5 className="font-medium text-gray-900">{project.name}</h5>
                      <span className="text-sm text-gray-600">${project.budget.toLocaleString()}</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max={project.budget * 2}
                      value={project.budget}
                      className="w-full"
                      onChange={() => {
                        // Real-time recalculation would go here
                      }}
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>$0</span>
                      <span>${(project.budget * 2).toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Impact Analysis</h4>
                <div className="border rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Real-time Metrics</h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Budget:</span>
                      <span className="font-medium">${optimizedAllocation?.total_budget.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Expected ROI:</span>
                      <span className="font-medium text-green-600">+15.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Risk Level:</span>
                      <span className="font-medium text-orange-600">Medium</span>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h5 className="font-medium text-gray-900 mb-3">Scenario Comparison</h5>
                  <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                    <span className="text-gray-500">Impact Chart</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">🏗️ CAPEX Optimization</h2>
        <p className="text-gray-600">
          AI-powered CAPEX prioritization with scoring, optimization, and what-if analysis.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors cursor-pointer
                  ${step.completed || currentStep === step.id
                    ? 'bg-indigo-600 border-indigo-600 text-white'
                    : 'bg-white border-gray-300 text-gray-400'
                  }
                `}
                onClick={() => setCurrentStep(step.id as any)}
              >
                {step.completed ? '✓' : step.icon}
              </div>
              <div className="ml-3 hidden md:block">
                <div className={`text-sm font-medium ${
                  step.completed || currentStep === step.id ? 'text-indigo-600' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  step.completed ? 'bg-indigo-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {renderStepContent()}
      </div>
    </div>
  );
}
