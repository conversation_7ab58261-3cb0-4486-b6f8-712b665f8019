'use client';

import { useState } from 'react';
import { DataUpload } from './data-upload';
import { MacroDashboard } from './macro-dashboard';

interface DataManagementProps {
  onDataUpdate?: (data: any) => void;
}

export function DataManagement({ onDataUpdate }: DataManagementProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'macro'>('upload');
  const [uploadedData, setUploadedData] = useState<any[]>([]);
  const [macroData, setMacroData] = useState<any>(null);

  const handleUploadSuccess = (data: any) => {
    setUploadedData(prev => [...prev, data]);
    onDataUpdate?.(data);
  };

  const handleUploadError = (error: string) => {
    console.error('Upload error:', error);
    // You could add a toast notification here
  };

  const handleMacroDataUpdate = (data: any) => {
    setMacroData(data);
    onDataUpdate?.(data);
  };

  const tabs = [
    { id: 'upload', label: 'File Upload', icon: '📁', description: 'Upload OPEX, CAPEX, and other data files' },
    { id: 'macro', label: 'Macro Economics', icon: '🌍', description: 'Monitor economic indicators and trends' },
  ] as const;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">📊 Data Management</h2>
        <p className="text-gray-600">
          Upload your financial data files and monitor macro economic indicators for comprehensive analysis.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <span className="mr-2 text-lg">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'upload' && (
            <div className="space-y-4">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">📁 File Upload</h3>
                <p className="text-gray-600">
                  Upload your OPEX 2024, CAPEX planning, revenue data, and other financial files for analysis.
                </p>
              </div>
              <DataUpload
                onUploadSuccess={handleUploadSuccess}
                onUploadError={handleUploadError}
              />
            </div>
          )}

          {activeTab === 'macro' && (
            <div className="space-y-4">
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">🌍 Macro Economics</h3>
                <p className="text-gray-600">
                  Monitor key economic indicators for your business markets and analyze their impact on your operations.
                </p>
              </div>
              <MacroDashboard onDataUpdate={handleMacroDataUpdate} />
            </div>
          )}
        </div>
      </div>

      {/* Data Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Uploaded Files Summary */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📁 Uploaded Data</h3>
          
          {uploadedData.length > 0 ? (
            <div className="space-y-3">
              {uploadedData.slice(0, 3).map((file, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center">
                    <span className="text-lg mr-3">
                      {file.data_type === 'opex' ? '💰' : 
                       file.data_type === 'capex' ? '🏗️' : 
                       file.data_type === 'revenue' ? '📈' : '📄'}
                    </span>
                    <div>
                      <div className="font-medium text-gray-900">{file.filename}</div>
                      <div className="text-sm text-gray-600">
                        {file.rows.toLocaleString()} rows • {file.columns} columns
                      </div>
                    </div>
                  </div>
                  <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                    {file.data_type.toUpperCase()}
                  </span>
                </div>
              ))}
              
              {uploadedData.length > 3 && (
                <div className="text-center text-sm text-gray-600">
                  +{uploadedData.length - 3} more files
                </div>
              )}
              
              <button
                onClick={() => setActiveTab('upload')}
                className="w-full mt-3 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                View All Files →
              </button>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-2">📁</div>
              <p className="text-gray-600 text-sm">No files uploaded yet</p>
              <button
                onClick={() => setActiveTab('upload')}
                className="mt-2 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                Upload Files →
              </button>
            </div>
          )}
        </div>

        {/* Macro Data Summary */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🌍 Macro Economics</h3>
          
          {macroData ? (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded">
                  <div className="text-2xl font-bold text-blue-600">
                    {macroData.countries?.length || 0}
                  </div>
                  <div className="text-sm text-blue-800">Countries</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded">
                  <div className="text-2xl font-bold text-green-600">
                    {macroData.indicators?.length || 0}
                  </div>
                  <div className="text-sm text-green-800">Indicators</div>
                </div>
              </div>
              
              {macroData.business_analysis?.key_insights && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-900 mb-2">💡 Latest Insights</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {macroData.business_analysis.key_insights.slice(0, 2).map((insight: string, index: number) => (
                      <li key={index} className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <button
                onClick={() => setActiveTab('macro')}
                className="w-full mt-3 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                View Full Analysis →
              </button>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-2">🌍</div>
              <p className="text-gray-600 text-sm">No macro data loaded yet</p>
              <button
                onClick={() => setActiveTab('macro')}
                className="mt-2 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
              >
                Load Macro Data →
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Integration Status */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">🔗 Data Integration Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${uploadedData.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm">
              <span className="font-medium">Financial Data:</span> {uploadedData.length > 0 ? 'Connected' : 'Not Connected'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${macroData ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm">
              <span className="font-medium">Macro Data:</span> {macroData ? 'Connected' : 'Not Connected'}
            </span>
          </div>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${uploadedData.length > 0 && macroData ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            <span className="text-sm">
              <span className="font-medium">Analysis Ready:</span> {uploadedData.length > 0 && macroData ? 'Yes' : 'Partial'}
            </span>
          </div>
        </div>
        
        {uploadedData.length > 0 && macroData && (
          <div className="mt-4 p-3 bg-green-100 rounded-lg">
            <p className="text-green-800 text-sm">
              ✅ <strong>Ready for Advanced Analysis!</strong> You now have both financial data and macro economic indicators loaded. 
              Your algorithms can now provide more comprehensive insights by combining internal data with external economic factors.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
