'use client';

import { useState, useRef } from 'react';
import { apiClient } from '@/lib/api';

interface DataUploadProps {
  onUploadSuccess?: (data: any) => void;
  onUploadError?: (error: string) => void;
}

export function DataUpload({ onUploadSuccess, onUploadError }: DataUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const dataTypes = [
    { value: 'opex', label: 'OPEX 2024', description: 'Operating expenses data' },
    { value: 'capex', label: 'CAPEX Planning', description: 'Capital expenditure planning' },
    { value: 'revenue', label: 'Revenue Data', description: 'Revenue and sales data' },
    { value: 'other', label: 'Other', description: 'Other financial data' },
  ];

  const handleFileUpload = async (file: File, dataType: string, description?: string) => {
    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('data_type', dataType);
      if (description) {
        formData.append('description', description);
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/data/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setUploadedFiles(prev => [...prev, result.data]);
        onUploadSuccess?.(result.data);
      } else {
        throw new Error(result.message || 'Upload failed');
      }

    } catch (error: any) {
      console.error('Upload error:', error);
      onUploadError?.(error.message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (validateFile(file)) {
        // For drag & drop, default to 'other' type - user can change later
        handleFileUpload(file, 'other');
      }
    }
  };

  const validateFile = (file: File): boolean => {
    const validTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    if (!validTypes.includes(fileExtension)) {
      onUploadError?.('Please upload Excel (.xlsx, .xls) or CSV files only');
      return false;
    }

    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      onUploadError?.('File size must be less than 50MB');
      return false;
    }

    return true;
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (validateFile(file)) {
        // Show file selection modal or handle upload
        handleFileUpload(file, 'other');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${dragActive 
            ? 'border-indigo-500 bg-indigo-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isUploading ? 'opacity-50 pointer-events-none' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {isUploading ? (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="text-gray-600">Uploading and processing file...</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-6xl">📁</div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Upload Your Data Files
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop your OPEX 2024, CAPEX planning, or other financial data files here
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Choose Files
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
            <p className="text-sm text-gray-500">
              Supports Excel (.xlsx, .xls) and CSV files up to 50MB
            </p>
          </div>
        )}
      </div>

      {/* Quick Upload Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {dataTypes.map((type) => (
          <div key={type.value} className="bg-white border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-1">{type.label}</h4>
            <p className="text-sm text-gray-600 mb-3">{type.description}</p>
            <button
              onClick={() => {
                fileInputRef.current?.click();
                // Store the selected type for when file is chosen
                fileInputRef.current?.setAttribute('data-type', type.value);
              }}
              className="w-full bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 transition-colors text-sm"
            >
              Upload {type.label}
            </button>
          </div>
        ))}
      </div>

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            📊 Uploaded Files ({uploadedFiles.length})
          </h3>
          <div className="space-y-4">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <span className="text-lg mr-2">
                        {file.data_type === 'opex' ? '💰' : 
                         file.data_type === 'capex' ? '🏗️' : 
                         file.data_type === 'revenue' ? '📈' : '📄'}
                      </span>
                      <h4 className="font-medium text-gray-900">{file.filename}</h4>
                      <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        {file.data_type.toUpperCase()}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Rows:</span> {file.rows.toLocaleString()}
                      </div>
                      <div>
                        <span className="font-medium">Columns:</span> {file.columns}
                      </div>
                      <div>
                        <span className="font-medium">Uploaded:</span> {new Date(file.upload_timestamp).toLocaleDateString()}
                      </div>
                      <div>
                        <span className="font-medium">Status:</span> 
                        <span className="text-green-600 ml-1">✅ Processed</span>
                      </div>
                    </div>
                    
                    {/* Initial Analysis Results */}
                    {file.initial_analysis && (
                      <div className="mt-3 p-3 bg-gray-50 rounded">
                        <h5 className="font-medium text-gray-900 mb-2">📋 Initial Analysis</h5>
                        {file.initial_analysis.insights && (
                          <ul className="text-sm text-gray-600 space-y-1">
                            {file.initial_analysis.insights.map((insight: string, i: number) => (
                              <li key={i}>• {insight}</li>
                            ))}
                          </ul>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 Data Upload Tips</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <h4 className="font-medium mb-2">OPEX Data Should Include:</h4>
            <ul className="space-y-1">
              <li>• Cost amounts and categories</li>
              <li>• Department or business unit</li>
              <li>• Time periods (monthly/quarterly)</li>
              <li>• Cost centers or GL accounts</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">CAPEX Data Should Include:</h4>
            <ul className="space-y-1">
              <li>• Project names and descriptions</li>
              <li>• Investment amounts and timelines</li>
              <li>• Project status and phases</li>
              <li>• Expected ROI or business case</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
