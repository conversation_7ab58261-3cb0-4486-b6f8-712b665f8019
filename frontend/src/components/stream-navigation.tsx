'use client';

interface StreamNavigationProps {
  currentStream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights';
  onStreamChange: (stream: 'dashboard' | 'data' | 'scenarios' | 'capex' | 'revenue' | 'insights') => void;
}

export function StreamNavigation({ currentStream, onStreamChange }: StreamNavigationProps) {
  const streams = [
    { 
      id: 'dashboard', 
      label: 'Dashboard', 
      icon: '📊',
      description: 'Overview and quick actions'
    },
    { 
      id: 'data', 
      label: 'Data Integration', 
      icon: '📁',
      description: 'Upload, validate & enrich data',
      workflow: 'Stream 1'
    },
    { 
      id: 'scenarios', 
      label: 'Scenario Planning', 
      icon: '🎯',
      description: 'Define constraints & run simulations',
      workflow: 'Stream 2'
    },
    { 
      id: 'capex', 
      label: 'CAPEX Optimization', 
      icon: '🏗️',
      description: 'Prioritize & optimize investments',
      workflow: 'Stream 3'
    },
    { 
      id: 'revenue', 
      label: 'Revenue Forecasting', 
      icon: '📈',
      description: 'Predict revenue & analyze drivers',
      workflow: 'Stream 4'
    },
    { 
      id: 'insights', 
      label: 'Insights & Reports', 
      icon: '📋',
      description: 'Generate insights & create reports',
      workflow: 'Stream 5'
    },
  ] as const;

  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Navigation */}
        <div className="flex space-x-8 overflow-x-auto">
          {streams.map((stream) => (
            <button
              key={stream.id}
              onClick={() => onStreamChange(stream.id)}
              className={`
                flex flex-col items-center py-4 px-3 border-b-2 font-medium text-sm transition-colors min-w-max
                ${currentStream === stream.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              <span className="text-2xl mb-1">{stream.icon}</span>
              <span className="font-semibold">{stream.label}</span>
              {stream.workflow && (
                <span className="text-xs text-gray-400 mt-1">{stream.workflow}</span>
              )}
            </button>
          ))}
        </div>

        {/* Stream Description */}
        <div className="py-3 border-t border-gray-100">
          {streams.map((stream) => (
            currentStream === stream.id && (
              <div key={stream.id} className="flex items-center text-sm text-gray-600">
                <span className="mr-2">{stream.icon}</span>
                <span>{stream.description}</span>
                {stream.workflow && (
                  <span className="ml-4 px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs">
                    {stream.workflow}
                  </span>
                )}
              </div>
            )
          ))}
        </div>
      </div>
    </div>
  );
}
