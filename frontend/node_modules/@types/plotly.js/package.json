{"name": "@types/plotly.js", "version": "3.0.3", "description": "TypeScript definitions for plotly.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/plotly.js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/chrisgervang"}, {"name": "<PERSON>", "githubUsername": "mart<PERSON>upar<PERSON>", "url": "https://github.com/martinduparc"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "frederi<PERSON><PERSON><PERSON>", "url": "https://github.com/frederikaalund"}, {"name": "taoqf", "githubUsername": "taoqf", "url": "https://github.com/taoqf"}, {"name": "Dadstart", "githubUsername": "Dadstart", "url": "https://github.com/Dadstart"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/szechyjs"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/soorajpudiyadath"}, {"name": "<PERSON>", "githubUsername": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/jonfreedman"}, {"name": "<PERSON>", "githubUsername": "meganrm", "url": "https://github.com/meganrm"}, {"name": "<PERSON>", "githubUsername": "milesjos", "url": "https://github.com/milesjos"}, {"name": "<PERSON><PERSON><PERSON> ", "githubUsername": "skippercool", "url": "https://github.com/skippercool"}, {"name": "<PERSON>", "githubUsername": "marnett-git", "url": "https://github.com/marnett-git"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "bra<PERSON><PERSON>", "url": "https://github.com/brammitch"}, {"name": "Jessica <PERSON>", "githubUsername": "blizzardjessica", "url": "https://github.com/blizzardjessica"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/oleg<PERSON>lov"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/PabloGracia"}, {"name": "<PERSON>", "githubUsername": "jvgogh", "url": "https://github.com/jvgogh"}, {"name": "<PERSON>", "githubUsername": "jpabdou", "url": "https://github.com/jpabdou"}, {"name": "<PERSON>", "githubUsername": "mrtnbrst", "url": "https://github.com/mrtnbrst"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/plotly.js"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "c27be3049d4c4c4a194c3ba1bba01cab9aadb7988b1e9ce097479f8471604444", "typeScriptVersion": "5.1"}