# Installation
> `npm install --save @types/plotly.js`

# Summary
This package contains type definitions for plotly.js (https://plot.ly/javascript/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/plotly.js.

### Additional Details
 * Last updated: <PERSON><PERSON>, 29 Jul 2025 03:13:25 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/chrisgervang), [<PERSON>](https://github.com/martinduparc), [<PERSON><PERSON><PERSON>](https://github.com/frederikaalund), [taoqf](https://github.com/taoqf), [Dadstart](https://github.com/Dadstart), [<PERSON>](https://github.com/szechyjs), [<PERSON><PERSON>udiyadath](https://github.com/soorajpudiyadath), [<PERSON>](https://github.com/jonfreedman), [<PERSON>](https://github.com/meganrm), [<PERSON>](https://github.com/milesjos), [<PERSON><PERSON><PERSON> ](https://github.com/skippercool), [Michael Arnett](https://github.com/marnett-git), [Piotr Błażejewicz](https://github.com/peterblazejewicz), [Brandon Mitchell](https://github.com/brammitch), [Jessica Blizzard](https://github.com/blizzardjessica), [Oleg Shilov](https://github.com/olegshilov), [Pablo Gracia](https://github.com/PabloGracia), [Jeffrey van Gogh](https://github.com/jvgogh), [John Abdou](https://github.com/jpabdou), and [Martin Borst](https://github.com/mrtnbrst).
