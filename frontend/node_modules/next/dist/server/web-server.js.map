{"version": 3, "sources": ["../../src/server/web-server.ts"], "sourcesContent": ["import type { WebNextRequest, WebNextResponse } from './base-http/web'\nimport type RenderResult from './render-result'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  LoadedRenderOpts,\n  MiddlewareRoutingItem,\n  NormalizedRouteManifest,\n  Options,\n  RouteHandler,\n} from './base-server'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { byteLength } from './api-utils/web'\nimport BaseServer from './base-server'\nimport { generateETag } from './lib/etag'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport WebResponseCache from './response-cache/web'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport {\n  interpolateDynamicPath,\n  normalizeCdnUrl,\n  normalizeDynamicRouteParams,\n} from './server-utils'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport type { PAGE_TYPES } from '../lib/page-types'\nimport type { Rewrite } from '../lib/load-custom-routes'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { UNDERSCORE_NOT_FOUND_ROUTE } from '../api/constants'\nimport { getEdgeInstrumentationModule } from './web/globals'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { getEdgePreviewProps } from './web/get-edge-preview-props'\nimport { NoFallbackError } from '../shared/lib/no-fallback-error.external'\n\ninterface WebServerOptions extends Options {\n  buildId: string\n  webServerConfig: {\n    page: string\n    pathname: string\n    pagesType: PAGE_TYPES\n    loadComponent: (page: string) => Promise<LoadComponentsReturnType | null>\n    extendRenderOpts: Partial<BaseServer['renderOpts']> & {\n      serverActionsManifest?: any\n    }\n    renderToHTML:\n      | typeof import('./app-render/app-render').renderToHTMLOrFlight\n      | undefined\n    incrementalCacheHandler?: any\n    interceptionRouteRewrites?: Rewrite[]\n  }\n}\n\ntype WebRouteHandler = RouteHandler<WebNextRequest, WebNextResponse>\n\nexport default class NextWebServer extends BaseServer<\n  WebServerOptions,\n  WebNextRequest,\n  WebNextResponse\n> {\n  constructor(options: WebServerOptions) {\n    super(options)\n\n    // Extend `renderOpts`.\n    Object.assign(this.renderOpts, options.webServerConfig.extendRenderOpts)\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n  }) {\n    const dev = !!this.renderOpts.dev\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      dev,\n      requestHeaders,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk: false,\n      CurCacheHandler:\n        this.serverOptions.webServerConfig.incrementalCacheHandler,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n    })\n  }\n  protected getResponseCache() {\n    return new WebResponseCache(this.minimalMode)\n  }\n\n  protected async hasPage(page: string) {\n    return page === this.serverOptions.webServerConfig.page\n  }\n\n  protected getBuildId() {\n    return this.serverOptions.buildId\n  }\n\n  protected getEnabledDirectories() {\n    return {\n      app: this.serverOptions.webServerConfig.pagesType === 'app',\n      pages: this.serverOptions.webServerConfig.pagesType === 'pages',\n    }\n  }\n\n  protected getPagesManifest() {\n    return {\n      // keep same theme but server path doesn't need to be accurate\n      [this.serverOptions.webServerConfig.pathname]:\n        `server${this.serverOptions.webServerConfig.page}.js`,\n    }\n  }\n\n  protected getAppPathsManifest() {\n    const page = this.serverOptions.webServerConfig.page\n    return {\n      [this.serverOptions.webServerConfig.page]: `app${page}.js`,\n    }\n  }\n\n  protected attachRequestMeta(\n    req: WebNextRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ) {\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n  }\n\n  protected getPrerenderManifest() {\n    return {\n      version: -1 as any, // letting us know this doesn't conform to spec\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: getEdgePreviewProps(),\n    }\n  }\n\n  protected getNextFontManifest() {\n    return this.serverOptions.webServerConfig.extendRenderOpts.nextFontManifest\n  }\n\n  protected handleCatchallRenderRequest: WebRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('pathname is undefined')\n    }\n\n    // interpolate query information into page for dynamic route\n    // so that rewritten paths are handled properly\n    const normalizedPage = this.serverOptions.webServerConfig.pathname\n\n    if (pathname !== normalizedPage) {\n      pathname = normalizedPage\n\n      if (isDynamicRoute(pathname)) {\n        const routeRegex = getNamedRouteRegex(pathname, {\n          prefixRouteKeys: false,\n        })\n        const dynamicRouteMatcher = getRouteMatcher(routeRegex)\n        const defaultRouteMatches = dynamicRouteMatcher(\n          pathname\n        ) as NextParsedUrlQuery\n        const paramsResult = normalizeDynamicRouteParams(\n          query,\n          routeRegex,\n          defaultRouteMatches,\n          false\n        )\n        const normalizedParams = paramsResult.hasValidParams\n          ? paramsResult.params\n          : query\n\n        pathname = interpolateDynamicPath(\n          pathname,\n          normalizedParams,\n          routeRegex\n        )\n        normalizeCdnUrl(req, Object.keys(routeRegex.routeKeys))\n      }\n    }\n\n    // next.js core assumes page path without trailing slash\n    pathname = removeTrailingSlash(pathname)\n\n    if (this.i18nProvider) {\n      const { detectedLocale } = await this.i18nProvider.analyze(pathname)\n      if (detectedLocale) {\n        addRequestMeta(req, 'locale', detectedLocale)\n      }\n    }\n\n    const bubbleNoFallback = getRequestMeta(req, 'bubbleNoFallback')\n\n    try {\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err) {\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  protected renderHTML(\n    req: WebNextRequest,\n    res: WebNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    const { renderToHTML } = this.serverOptions.webServerConfig\n    if (!renderToHTML) {\n      throw new Error(\n        'Invariant: routeModule should be configured when rendering pages'\n      )\n    }\n\n    // For edge runtime if the pathname hit as /_not-found entrypoint,\n    // override the pathname to /404 for rendering\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    return renderToHTML(\n      req as any,\n      res as any,\n      pathname,\n      query,\n      // Edge runtime does not support ISR/PPR, so we don't need to pass in\n      // the unknown params.\n      null,\n      Object.assign(renderOpts, {\n        disableOptimizedLoading: true,\n        runtime: 'experimental-edge',\n      }),\n      undefined,\n      false,\n      {\n        buildId: this.serverOptions.buildId,\n      }\n    )\n  }\n\n  protected async sendRenderResult(\n    _req: WebNextRequest,\n    res: WebNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    res.setHeader('X-Edge-Runtime', '1')\n\n    // Add necessary headers.\n    // @TODO: Share the isomorphic logic with server/send-payload.ts.\n    if (options.poweredByHeader && options.type === 'html') {\n      res.setHeader('X-Powered-By', 'Next.js')\n    }\n\n    if (!res.getHeader('Content-Type')) {\n      res.setHeader(\n        'Content-Type',\n        options.result.contentType\n          ? options.result.contentType\n          : options.type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n      )\n    }\n\n    let promise: Promise<void> | undefined\n    if (options.result.isDynamic) {\n      promise = options.result.pipeTo(res.transformStream.writable)\n    } else {\n      const payload = options.result.toUnchunkedString()\n      res.setHeader('Content-Length', String(byteLength(payload)))\n      if (options.generateEtags) {\n        res.setHeader('ETag', generateETag(payload))\n      }\n      res.body(payload)\n    }\n\n    res.send()\n\n    // If we have a promise, wait for it to resolve.\n    if (promise) await promise\n  }\n\n  protected async findPageComponents({\n    page,\n    query,\n    params,\n    url: _url,\n  }: {\n    page: string\n    query: NextParsedUrlQuery\n    params: Params | null\n    isAppPath: boolean\n    url?: string\n  }) {\n    const result = await this.serverOptions.webServerConfig.loadComponent(page)\n    if (!result) return null\n\n    return {\n      query: {\n        ...(query || {}),\n        ...(params || {}),\n      },\n      components: result,\n    }\n  }\n\n  // Below are methods that are not implemented by the web server as they are\n  // handled by the upstream proxy (edge runtime or node server).\n\n  protected async runApi() {\n    // This web server does not need to handle API requests.\n    return true\n  }\n\n  protected async handleApiRequest() {\n    // Edge API requests are handled separately in minimal mode.\n    return false\n  }\n\n  protected loadEnvConfig() {\n    // The web server does not need to load the env config. This is done by the\n    // runtime already.\n  }\n\n  protected getPublicDir() {\n    // Public files are not handled by the web server.\n    return ''\n  }\n\n  protected getHasStaticDir() {\n    return false\n  }\n\n  protected getFontManifest() {\n    return undefined\n  }\n\n  protected handleCompression() {\n    // For the web server layer, compression is automatically handled by the\n    // upstream proxy (edge runtime or node server) and we can simply skip here.\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets.\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // The web server does not need to handle fallback errors in production.\n    return null\n  }\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    // The web server does not need to handle rewrite rules. This is done by the\n    // upstream proxy (edge runtime or node server).\n    return undefined\n  }\n\n  protected getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    // The web server does not need to handle middleware. This is done by the\n    // upstream proxy (edge runtime or node server).\n    return Promise.resolve(undefined)\n  }\n\n  protected getFilesystemPaths() {\n    return new Set<string>()\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    return (\n      this.serverOptions.webServerConfig.interceptionRouteRewrites?.map(\n        (rewrite) => new RegExp(buildCustomRoute('rewrite', rewrite).regex)\n      ) ?? []\n    )\n  }\n\n  protected async loadInstrumentationModule() {\n    return await getEdgeInstrumentationModule()\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n    const err = args[0]\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof __next_log_error__ === 'function'\n    ) {\n      __next_log_error__(err)\n    } else {\n      console.error(err)\n    }\n  }\n}\n"], "names": ["NextWebServer", "BaseServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "dynamicRouteMatcher", "getRouteMatcher", "defaultRouteMatches", "paramsResult", "normalizeDynamicRouteParams", "normalizedParams", "hasValidParams", "params", "interpolateDynamicPath", "normalizeCdnUrl", "Object", "keys", "routeKeys", "removeTrailingSlash", "i18nProvider", "detectedLocale", "analyze", "addRequestMeta", "bubbleNoFallback", "getRequestMeta", "render", "err", "NoFallbackError", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "IncrementalCache", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "WebResponseCache", "hasPage", "page", "getBuildId", "buildId", "getEnabledDirectories", "app", "pagesType", "pages", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getEdgePreviewProps", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "UNDERSCORE_NOT_FOUND_ROUTE", "disableOptimizedLoading", "runtime", "undefined", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "byteLength", "generateEtags", "generateETag", "body", "send", "findPageComponents", "url", "_url", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "getFontManifest", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "Promise", "resolve", "getFilesystemPaths", "Set", "getinterceptionRoutePatterns", "interceptionRouteRewrites", "map", "rewrite", "RegExp", "buildCustomRoute", "regex", "loadInstrumentationModule", "getEdgeInstrumentationModule", "instrumentationOnRequestError", "args", "process", "env", "NODE_ENV", "__next_log_error__", "console", "error"], "mappings": ";;;;+BA0DA;;;eAAqBA;;;qBA5CM;mEACJ;sBACM;6BACkB;6DAClB;qCACO;uBACL;6BAKxB;4BAC4B;8BACH;kCACC;kCAGA;2BACU;yBACE;qCAET;yCACJ;;;;;;AAsBjB,MAAMA,sBAAsBC,mBAAU;IAKnDC,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA,eAoFEC,8BAA+C,OACvDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAAkC,CAAlC,IAAIE,MAAM,0BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiC;YACzC;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIG,IAAAA,qBAAc,EAACN,WAAW;oBAC5B,MAAMO,aAAaC,IAAAA,8BAAkB,EAACR,UAAU;wBAC9CS,iBAAiB;oBACnB;oBACA,MAAMC,sBAAsBC,IAAAA,6BAAe,EAACJ;oBAC5C,MAAMK,sBAAsBF,oBAC1BV;oBAEF,MAAMa,eAAeC,IAAAA,wCAA2B,EAC9Cb,OACAM,YACAK,qBACA;oBAEF,MAAMG,mBAAmBF,aAAaG,cAAc,GAChDH,aAAaI,MAAM,GACnBhB;oBAEJD,WAAWkB,IAAAA,mCAAsB,EAC/BlB,UACAe,kBACAR;oBAEFY,IAAAA,4BAAe,EAACtB,KAAKuB,OAAOC,IAAI,CAACd,WAAWe,SAAS;gBACvD;YACF;YAEA,wDAAwD;YACxDtB,WAAWuB,IAAAA,wCAAmB,EAACvB;YAE/B,IAAI,IAAI,CAACwB,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAAC1B;gBAC3D,IAAIyB,gBAAgB;oBAClBE,IAAAA,2BAAc,EAAC9B,KAAK,UAAU4B;gBAChC;YACF;YAEA,MAAMG,mBAAmBC,IAAAA,2BAAc,EAAChC,KAAK;YAE7C,IAAI;gBACF,MAAM,IAAI,CAACiC,MAAM,CAACjC,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOgC,KAAK;gBACZ,IAAIA,eAAeC,wCAAe,IAAIJ,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMG;YACR;QACF;QApJE,uBAAuB;QACvBX,OAAOa,MAAM,CAAC,IAAI,CAACC,UAAU,EAAEvC,QAAQU,eAAe,CAAC8B,gBAAgB;IACzE;IAEA,MAAgBC,oBAAoB,EAClCC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIC,kCAAgB,CAAC;YAC1BD;YACAD;YACAG,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,qBAAqB,IAAI,CAACH,UAAU,CAACC,YAAY,CAACE,mBAAmB;YACrEC,oBAAoB,IAAI,CAACJ,UAAU,CAACK,kBAAkB;YACtDC,aAAa;YACbC,iBACE,IAAI,CAAC5C,aAAa,CAACC,eAAe,CAAC4C,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIC,aAAgB,CAAC,IAAI,CAACT,WAAW;IAC9C;IAEA,MAAgBU,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACnD,aAAa,CAACoD,OAAO;IACnC;IAEUC,wBAAwB;QAChC,OAAO;YACLC,KAAK,IAAI,CAACtD,aAAa,CAACC,eAAe,CAACsD,SAAS,KAAK;YACtDC,OAAO,IAAI,CAACxD,aAAa,CAACC,eAAe,CAACsD,SAAS,KAAK;QAC1D;IACF;IAEUE,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACzD,aAAa,CAACC,eAAe,CAACL,QAAQ,CAAC,EAC3C,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAACiD,IAAI,CAAC,GAAG,CAAC;QACzD;IACF;IAEUQ,sBAAsB;QAC9B,MAAMR,OAAO,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAAClD,aAAa,CAACC,eAAe,CAACiD,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUS,kBACRlE,GAAmB,EACnBE,SAAiC,EACjC;QACA4B,IAAAA,2BAAc,EAAC9B,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEUiD,uBAAuB;QAC/B,OAAO;YACLc,SAAS,CAAC;YACVC,QAAQ,CAAC;YACTC,eAAe,CAAC;YAChBC,gBAAgB,EAAE;YAClBC,SAASC,IAAAA,wCAAmB;QAC9B;IACF;IAEUC,sBAAsB;QAC9B,OAAO,IAAI,CAAClE,aAAa,CAACC,eAAe,CAAC8B,gBAAgB,CAACoC,gBAAgB;IAC7E;IAsEUC,WACR3E,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzBiC,UAA4B,EACL;QACvB,MAAM,EAAEuC,YAAY,EAAE,GAAG,IAAI,CAACrE,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACoE,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAIvE,MACR,qEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAa0E,qCAA0B,EAAE;YAC3C1E,WAAW;QACb;QACA,OAAOyE,aACL5E,KACAC,KACAE,UACAC,OACA,qEAAqE;QACrE,sBAAsB;QACtB,MACAmB,OAAOa,MAAM,CAACC,YAAY;YACxByC,yBAAyB;YACzBC,SAAS;QACX,IACAC,WACA,OACA;YACErB,SAAS,IAAI,CAACpD,aAAa,CAACoD,OAAO;QACrC;IAEJ;IAEA,MAAgBsB,iBACdC,IAAoB,EACpBjF,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAIkF,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAIrF,QAAQsF,eAAe,IAAItF,QAAQuF,IAAI,KAAK,QAAQ;YACtDpF,IAAIkF,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAClF,IAAIqF,SAAS,CAAC,iBAAiB;YAClCrF,IAAIkF,SAAS,CACX,gBACArF,QAAQyF,MAAM,CAACC,WAAW,GACtB1F,QAAQyF,MAAM,CAACC,WAAW,GAC1B1F,QAAQuF,IAAI,KAAK,SACf,qBACA;QAEV;QAEA,IAAII;QACJ,IAAI3F,QAAQyF,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAU3F,QAAQyF,MAAM,CAACI,MAAM,CAAC1F,IAAI2F,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAUhG,QAAQyF,MAAM,CAACQ,iBAAiB;YAChD9F,IAAIkF,SAAS,CAAC,kBAAkBa,OAAOC,IAAAA,eAAU,EAACH;YAClD,IAAIhG,QAAQoG,aAAa,EAAE;gBACzBjG,IAAIkF,SAAS,CAAC,QAAQgB,IAAAA,kBAAY,EAACL;YACrC;YACA7F,IAAImG,IAAI,CAACN;QACX;QAEA7F,IAAIoG,IAAI;QAER,gDAAgD;QAChD,IAAIZ,SAAS,MAAMA;IACrB;IAEA,MAAgBa,mBAAmB,EACjC7C,IAAI,EACJrD,KAAK,EACLgB,MAAM,EACNmF,KAAKC,IAAI,EAOV,EAAE;QACD,MAAMjB,SAAS,MAAM,IAAI,CAAChF,aAAa,CAACC,eAAe,CAACiG,aAAa,CAAChD;QACtE,IAAI,CAAC8B,QAAQ,OAAO;QAEpB,OAAO;YACLnF,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIgB,UAAU,CAAC,CAAC;YAClB;YACAsF,YAAYnB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBoB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOhC;IACT;IAEUiC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,2BACdX,IAAa,EAC6B;QAC1C,wEAAwE;QACxE,OAAO;IACT;IACUY,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOpC;IACT;IAEUqC,gBAA4D;QACpE,yEAAyE;QACzE,gDAAgD;QAChD,OAAOC,QAAQC,OAAO,CAACvC;IACzB;IAEUwC,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEUC,+BAAyC;YAE/C;QADF,OACE,EAAA,gEAAA,IAAI,CAACnH,aAAa,CAACC,eAAe,CAACmH,yBAAyB,qBAA5D,8DAA8DC,GAAG,CAC/D,CAACC,UAAY,IAAIC,OAAOC,IAAAA,kCAAgB,EAAC,WAAWF,SAASG,KAAK,OAC/D,EAAE;IAEX;IAEA,MAAgBC,4BAA4B;QAC1C,OAAO,MAAMC,IAAAA,qCAA4B;IAC3C;IAEA,MAAgBC,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAC7C,MAAMlG,MAAMkG,IAAI,CAAC,EAAE;QAEnB,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzB,OAAOC,uBAAuB,YAC9B;YACAA,mBAAmBtG;QACrB,OAAO;YACLuG,QAAQC,KAAK,CAACxG;QAChB;IACF;AACF", "ignoreList": [0]}