/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.17 4.193a2 2 0 0 1 3.666.013", key: "pltmmw" }],
  ["path", { d: "M14 21h2", key: "v4qezv" }],
  ["path", { d: "m15.874 7.743 1 1.732", key: "10m0iw" }],
  ["path", { d: "m18.849 12.952 1 1.732", key: "zadnam" }],
  ["path", { d: "M21.824 18.18a2 2 0 0 1-1.835 2.824", key: "fvwuk4" }],
  ["path", { d: "M4.024 21a2 2 0 0 1-1.839-2.839", key: "1e1kah" }],
  ["path", { d: "m5.136 12.952-1 1.732", key: "1u4ldi" }],
  ["path", { d: "M8 21h2", key: "i9zjee" }],
  ["path", { d: "m8.102 7.743-1 1.732", key: "1zzo4u" }]
];
const TriangleDashed = createLucideIcon("triangle-dashed", __iconNode);

export { __iconNode, TriangleDashed as default };
//# sourceMappingURL=triangle-dashed.js.map
