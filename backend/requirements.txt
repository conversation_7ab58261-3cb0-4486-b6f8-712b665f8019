# Core AI and Agent Framework
crewai>=0.1.0
openai>=1.0.0
langchain>=0.1.0
langchain-openai>=0.1.0

# Data Processing and Analysis
pandas>=2.0.0
numpy>=1.24.0
pandasai>=1.0.0
openpyxl>=3.1.0
xlrd>=2.0.0

# Time Series Forecasting
prophet>=1.1.0
neuralprophet>=0.6.0

# Bayesian Inference and Risk Analysis
pymc>=5.0.0
arviz>=0.15.0
numpyro>=0.12.0
jax>=0.4.0
jaxlib>=0.4.0

# Web Interface
streamlit>=1.28.0
streamlit-chat>=0.1.0
plotly>=5.17.0

# FastAPI Backend
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
websockets>=11.0.0

# External APIs and Data Sources
requests>=2.31.0
aiohttp>=3.8.0

# Database and Storage
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
redis>=5.0.0

# Scientific Computing
scipy>=1.11.0
scikit-learn>=1.3.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
pyyaml>=6.0.0
rich>=13.0.0
click>=8.1.0

# Development Tools
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Report Generation
jinja2>=3.1.0
python-pptx>=0.6.0
fpdf2>=2.7.0

# Additional Dependencies for New Features
# Historical Data Analysis
sqlite3  # Built-in Python module
matplotlib>=3.7.0
seaborn>=0.12.0

# Statistical Analysis
statsmodels>=0.14.0

# Error Handling and Logging
tenacity>=8.2.0
loguru>=0.7.0

# Natural Language Processing
nltk>=3.8.0
spacy>=3.6.0

# Data Visualization
bokeh>=3.2.0
altair>=5.0.0

# Time and Date Handling
python-dateutil>=2.8.0
pytz>=2023.3
# JSON and Configuration
jsonschema>=4.17.0
toml>=0.10.0

# Performance and Optimization
numba>=0.57.0
cython>=3.0.0
