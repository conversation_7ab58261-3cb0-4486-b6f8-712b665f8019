"""
FastAPI Backend for OneOptimizer.

This is the main FastAPI application that exposes the sophisticated
AI models and analysis algorithms through REST API endpoints.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime
import pandas as pd
import os
import tempfile
import json

# Import your existing components
from agents.orchestrator_agent import OrchestratorAgent, WorkflowRequest
from services.analysis_service import AnalysisService
from services.forecast_service import ForecastService
from services.wizard_service import WizardService
from models.econometric_models import TimeSeriesAnalyzer, ForecastingEngine
from models.financial_ratios import FinancialRatiosCalculator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="OneOptimizer API",
    description="AI-Powered Budgeting System API with sophisticated analysis algorithms",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Configure CORS for Next.js frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
orchestrator = OrchestratorAgent(verbose=True)
analysis_service = AnalysisService()
forecast_service = ForecastService()
wizard_service = WizardService()

# Algorithm instances for direct access
time_series_analyzer = TimeSeriesAnalyzer()
forecasting_engine = ForecastingEngine()
financial_ratios = FinancialRatiosCalculator()

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "OneOptimizer API",
        "version": "2.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "algorithms": "/api/algorithms",
            "analysis": "/api/analysis",
            "forecast": "/api/forecast",
            "wizard": "/api/wizard",
            "docs": "/api/docs"
        }
    }

@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "orchestrator": "running",
            "analysis_service": "running",
            "forecast_service": "running",
            "wizard_service": "running"
        }
    }

@app.get("/api/algorithms")
async def get_available_algorithms():
    """Get list of available algorithms and their capabilities."""
    try:
        algorithms = {
            "econometric_models": {
                "time_series_analyzer": {
                    "name": "Time Series Analyzer",
                    "description": "ARIMA, seasonal decomposition, trend analysis",
                    "capabilities": ["trend_analysis", "seasonality_detection", "business_insights"],
                    "input_requirements": ["time_series_data"],
                    "output_format": "business_insights_with_recommendations"
                },
                "forecasting_engine": {
                    "name": "Forecasting Engine", 
                    "description": "Auto-ARIMA with cross-validation",
                    "capabilities": ["revenue_forecasting", "confidence_intervals", "scenario_modeling"],
                    "input_requirements": ["historical_data", "forecast_periods"],
                    "output_format": "forecast_with_confidence_bands"
                }
            },
            "financial_analysis": {
                "financial_ratios": {
                    "name": "Financial Ratios Calculator",
                    "description": "Industry-specific ratio analysis for telecom/fintech",
                    "capabilities": ["profitability_analysis", "customer_metrics", "efficiency_ratios"],
                    "input_requirements": ["financial_data"],
                    "output_format": "ratio_analysis_with_benchmarks"
                }
            },
            "ai_agents": {
                "revenue_agent": {
                    "name": "Revenue Agent",
                    "description": "AI-powered revenue forecasting with Bayesian capabilities",
                    "capabilities": ["bayesian_forecasting", "arpu_analysis", "churn_prediction"],
                    "input_requirements": ["revenue_data", "business_context"],
                    "output_format": "comprehensive_revenue_analysis"
                },
                "scenario_agent": {
                    "name": "Scenario Agent",
                    "description": "Monte Carlo simulation and scenario generation",
                    "capabilities": ["monte_carlo_simulation", "risk_assessment", "sensitivity_analysis"],
                    "input_requirements": ["base_assumptions", "risk_parameters"],
                    "output_format": "scenario_analysis_with_probabilities"
                }
            }
        }
        
        return {
            "success": True,
            "algorithms": algorithms,
            "total_count": sum(len(category) for category in algorithms.values()),
            "categories": list(algorithms.keys())
        }
        
    except Exception as e:
        logger.error(f"Error getting algorithms: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get algorithms: {str(e)}")

@app.post("/api/analysis/time-series")
async def analyze_time_series(request: Dict[str, Any]):
    """Run time series analysis on provided data."""
    try:
        data = request.get("data")
        context = request.get("context", "revenue")
        use_real_data = request.get("use_real_data", False)
        file_path = request.get("file_path")

        # Load data from file if using real data
        if use_real_data and file_path:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                # Extract time series data from the uploaded file
                # This is a simplified extraction - in production, you'd have more sophisticated logic
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    data = df[numeric_cols[0]].dropna().tolist()
                else:
                    raise HTTPException(status_code=400, detail="No numeric data found in uploaded file")
            else:
                raise HTTPException(status_code=404, detail="Uploaded file not found")
        elif not data:
            raise HTTPException(status_code=400, detail="No data provided")

        # Use analysis service for comprehensive analysis
        analysis_config = {
            "time_series_data": {
                "data": data,
                "context": context,
                "data_source": "uploaded_file" if use_real_data else "sample_data"
            }
        }

        result = analysis_service.run_comprehensive_analysis(analysis_config)

        return {
            "success": True,
            "analysis": result,
            "algorithm": "time_series_analyzer",
            "data_source": "uploaded_file" if use_real_data else "sample_data",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Time series analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/analysis/forecast")
async def create_forecast(request: Dict[str, Any]):
    """Create forecast using econometric or AI methods."""
    try:
        data = request.get("data")
        periods = request.get("periods", 12)
        method = request.get("method", "econometric")  # econometric, ai, or hybrid
        context = request.get("context", "revenue")
        use_real_data = request.get("use_real_data", False)
        file_path = request.get("file_path")

        # Load data from file if using real data
        if use_real_data and file_path:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                # Extract time series data from the uploaded file
                numeric_cols = df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    data = df[numeric_cols[0]].dropna().tolist()
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="No numeric data found in uploaded file"
                    )
            else:
                raise HTTPException(status_code=404, detail="Uploaded file not found")
        elif not data:
            raise HTTPException(status_code=400, detail="No data provided")
        
        # Configure forecast based on method
        if method == "econometric":
            forecast_config = {
                "econometric_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        elif method == "ai":
            forecast_config = {
                "ai_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        else:  # hybrid
            forecast_config = {
                "econometric_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                },
                "ai_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        
        result = forecast_service.create_comprehensive_forecast(forecast_config)
        
        return {
            "success": True,
            "forecast": result,
            "method": method,
            "periods": periods,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Forecast error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Forecast failed: {str(e)}")

@app.post("/api/analysis/financial-ratios")
async def analyze_financial_ratios(request: Dict[str, Any]):
    """Calculate financial ratios and business insights."""
    try:
        financial_data = request.get("financial_data")
        industry_type = request.get("industry_type", "telecom")

        if not financial_data:
            raise HTTPException(status_code=400, detail="No financial data provided")

        # Initialize calculator with industry type
        calculator = FinancialRatiosCalculator(industry_type=industry_type)
        result = calculator.calculate_comprehensive_ratios(financial_data)

        return {
            "success": True,
            "analysis": result,
            "algorithm": "financial_ratios_calculator",
            "industry_type": industry_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Financial ratios analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/agents/revenue")
async def run_revenue_agent(request: Dict[str, Any]):
    """Run revenue agent analysis with AI capabilities."""
    try:
        # Use orchestrator to delegate to revenue agent
        workflow_request = WorkflowRequest(
            task_type="generate_revenue_forecast",
            input_data=request,
            user_message=request.get("user_message", "Generate revenue forecast"),
            session_id=request.get("session_id", "api_session")
        )

        response = orchestrator.process(workflow_request)

        return {
            "success": response.success,
            "data": response.data,
            "message": response.message,
            "agent": "revenue_agent",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Revenue agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Revenue agent failed: {str(e)}")

@app.post("/api/agents/scenario")
async def run_scenario_agent(request: Dict[str, Any]):
    """Run scenario agent for Monte Carlo simulation."""
    try:
        workflow_request = WorkflowRequest(
            task_type="generate_scenarios",
            input_data=request,
            user_message=request.get("user_message", "Generate scenarios"),
            session_id=request.get("session_id", "api_session")
        )

        response = orchestrator.process(workflow_request)

        return {
            "success": response.success,
            "data": response.data,
            "message": response.message,
            "agent": "scenario_agent",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Scenario agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scenario agent failed: {str(e)}")

@app.post("/api/wizard/process")
async def run_wizard_process(request: Dict[str, Any]):
    """Run wizard service for guided analysis."""
    try:
        process_type = request.get("process_type", "comprehensive")

        if process_type == "file_upload":
            files = request.get("files", [])
            result = wizard_service.process_uploaded_files(files)
        elif process_type == "growth_analysis":
            result = wizard_service.analyze_growth_assumptions(request)
        elif process_type == "risk_analysis":
            result = wizard_service.analyze_risk_scenarios(request)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown process type: {process_type}")

        return {
            "success": True,
            "result": result,
            "process_type": process_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Wizard process error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Wizard process failed: {str(e)}")

@app.post("/api/data/upload")
async def upload_data_file(
    file: UploadFile = File(...),
    data_type: str = Form(...),  # "opex", "capex", "revenue", "other"
    description: str = Form(None)
):
    """Upload and process OPEX, CAPEX, or other data files."""
    try:
        # Validate file type
        if not file.filename.endswith(('.xlsx', '.xls', '.csv')):
            raise HTTPException(status_code=400, detail="Only Excel (.xlsx, .xls) and CSV files are supported")

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Process the file based on type
            if file.filename.endswith('.csv'):
                df = pd.read_csv(temp_file_path)
            else:
                df = pd.read_excel(temp_file_path)

            # Basic data validation and processing
            processed_data = {
                "filename": file.filename,
                "data_type": data_type,
                "description": description,
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": df.columns.tolist(),
                "data_preview": df.head(5).to_dict('records'),
                "data_summary": {
                    "numeric_columns": df.select_dtypes(include=['number']).columns.tolist(),
                    "text_columns": df.select_dtypes(include=['object']).columns.tolist(),
                    "date_columns": df.select_dtypes(include=['datetime']).columns.tolist(),
                },
                "upload_timestamp": datetime.now().isoformat()
            }

            # Store the full data (in production, you'd save to database)
            data_storage_path = f"data/uploaded/{data_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
            os.makedirs(os.path.dirname(data_storage_path), exist_ok=True)
            df.to_csv(data_storage_path, index=False)
            processed_data["storage_path"] = data_storage_path

            # Run initial analysis based on data type
            if data_type == "opex":
                analysis_result = analyze_opex_data(df)
            elif data_type == "capex":
                analysis_result = analyze_capex_data(df)
            else:
                analysis_result = analyze_general_data(df)

            processed_data["initial_analysis"] = analysis_result

            return {
                "success": True,
                "data": processed_data,
                "message": f"Successfully uploaded and processed {file.filename}",
                "timestamp": datetime.now().isoformat()
            }

        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)

    except Exception as e:
        logger.error(f"File upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@app.get("/api/data/list")
async def list_uploaded_data():
    """List all uploaded data files."""
    try:
        data_dir = "data/uploaded"
        if not os.path.exists(data_dir):
            return {"success": True, "files": [], "count": 0}

        files = []
        for filename in os.listdir(data_dir):
            if filename.endswith('.csv'):
                file_path = os.path.join(data_dir, filename)
                file_stats = os.stat(file_path)

                # Extract metadata from filename
                parts = filename.split('_')
                data_type = parts[0] if parts else "unknown"

                files.append({
                    "filename": filename,
                    "data_type": data_type,
                    "size_bytes": file_stats.st_size,
                    "upload_date": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    "file_path": file_path
                })

        return {
            "success": True,
            "files": sorted(files, key=lambda x: x["upload_date"], reverse=True),
            "count": len(files),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")

def analyze_opex_data(df: pd.DataFrame) -> Dict[str, Any]:
    """Analyze OPEX data and provide insights."""
    try:
        analysis = {
            "data_type": "opex",
            "total_records": len(df),
            "insights": []
        }

        # Look for common OPEX columns
        amount_cols = [col for col in df.columns if any(term in col.lower() for term in ['amount', 'cost', 'expense', 'spend'])]
        category_cols = [col for col in df.columns if any(term in col.lower() for term in ['category', 'type', 'department', 'unit'])]
        date_cols = [col for col in df.columns if any(term in col.lower() for term in ['date', 'month', 'period', 'year'])]

        if amount_cols:
            amount_col = amount_cols[0]
            total_opex = df[amount_col].sum() if pd.api.types.is_numeric_dtype(df[amount_col]) else 0
            analysis["total_opex"] = float(total_opex)
            analysis["insights"].append(f"Total OPEX: ${total_opex:,.2f}")

            if category_cols:
                category_col = category_cols[0]
                category_breakdown = df.groupby(category_col)[amount_col].sum().to_dict()
                analysis["category_breakdown"] = {k: float(v) for k, v in category_breakdown.items()}
                analysis["insights"].append(f"Found {len(category_breakdown)} expense categories")

        analysis["suggested_columns"] = {
            "amount_columns": amount_cols,
            "category_columns": category_cols,
            "date_columns": date_cols
        }

        return analysis

    except Exception as e:
        return {"error": f"OPEX analysis failed: {str(e)}"}

def analyze_capex_data(df: pd.DataFrame) -> Dict[str, Any]:
    """Analyze CAPEX data and provide insights."""
    try:
        analysis = {
            "data_type": "capex",
            "total_records": len(df),
            "insights": []
        }

        # Look for common CAPEX columns
        amount_cols = [col for col in df.columns if any(term in col.lower() for term in ['amount', 'investment', 'capex', 'budget'])]
        project_cols = [col for col in df.columns if any(term in col.lower() for term in ['project', 'initiative', 'name', 'description'])]
        status_cols = [col for col in df.columns if any(term in col.lower() for term in ['status', 'phase', 'stage'])]

        if amount_cols:
            amount_col = amount_cols[0]
            total_capex = df[amount_col].sum() if pd.api.types.is_numeric_dtype(df[amount_col]) else 0
            analysis["total_capex"] = float(total_capex)
            analysis["insights"].append(f"Total CAPEX: ${total_capex:,.2f}")

            if project_cols:
                project_col = project_cols[0]
                project_count = df[project_col].nunique()
                analysis["project_count"] = project_count
                analysis["insights"].append(f"Found {project_count} unique projects")

        analysis["suggested_columns"] = {
            "amount_columns": amount_cols,
            "project_columns": project_cols,
            "status_columns": status_cols
        }

        return analysis

    except Exception as e:
        return {"error": f"CAPEX analysis failed: {str(e)}"}

def analyze_general_data(df: pd.DataFrame) -> Dict[str, Any]:
    """Analyze general data and provide insights."""
    try:
        analysis = {
            "data_type": "general",
            "total_records": len(df),
            "insights": []
        }

        # Basic statistics
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            analysis["numeric_summary"] = df[numeric_cols].describe().to_dict()
            analysis["insights"].append(f"Found {len(numeric_cols)} numeric columns")

        # Missing data analysis
        missing_data = df.isnull().sum()
        if missing_data.sum() > 0:
            analysis["missing_data"] = missing_data.to_dict()
            analysis["insights"].append(f"Dataset has {missing_data.sum()} missing values")

        return analysis

    except Exception as e:
        return {"error": f"General analysis failed: {str(e)}"}

@app.get("/api/macro/indicators")
async def get_macro_indicators(
    countries: str = "US,GB,CL,CO,OM",  # Default countries
    indicators: str = "GDP,INFLATION,UNEMPLOYMENT,INTEREST_RATE",
    years: str = "2022,2023,2024"
):
    """Get macro economic indicators for specified countries."""
    try:
        country_list = [c.strip() for c in countries.split(',')]
        indicator_list = [i.strip() for i in indicators.split(',')]
        year_list = [int(y.strip()) for y in years.split(',')]

        # Use existing MacroDataAgent
        from agents.macro_data_agent import MacroDataAgent, MacroDataRequest

        macro_agent = MacroDataAgent()

        results = {}
        for country in country_list:
            country_data = {}
            for indicator in indicator_list:
                try:
                    # Create request for macro data
                    request = MacroDataRequest(
                        countries=[country],
                        indicators=[indicator],
                        start_year=min(year_list),
                        end_year=max(year_list)
                    )

                    response = macro_agent.process(request)
                    if response.success:
                        country_data[indicator] = response.data
                    else:
                        country_data[indicator] = {"error": response.error}

                except Exception as e:
                    country_data[indicator] = {"error": str(e)}

            results[country] = country_data

        return {
            "success": True,
            "data": results,
            "countries": country_list,
            "indicators": indicator_list,
            "years": year_list,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Macro indicators error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch macro indicators: {str(e)}")

@app.get("/api/macro/countries")
async def get_available_countries():
    """Get list of available countries for macro data."""
    try:
        from agents.macro_data_agent import MacroDataAgent

        macro_agent = MacroDataAgent()
        countries = macro_agent.get_available_countries()

        return {
            "success": True,
            "countries": countries,
            "count": len(countries),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting countries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get countries: {str(e)}")

@app.get("/api/macro/indicators/list")
async def get_available_indicators():
    """Get list of available macro economic indicators."""
    try:
        from agents.macro_data_agent import MacroDataAgent

        macro_agent = MacroDataAgent()
        indicators = macro_agent.get_available_indicators()

        return {
            "success": True,
            "indicators": indicators,
            "count": len(indicators),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting indicators: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get indicators: {str(e)}")

@app.post("/api/macro/analysis")
async def analyze_macro_data(request: Dict[str, Any]):
    """Analyze macro economic data for business impact."""
    try:
        countries = request.get("countries", ["US", "CL", "CO"])
        business_context = request.get("business_context", "telecom")
        analysis_type = request.get("analysis_type", "comprehensive")

        from agents.macro_data_agent import MacroDataAgent, MacroDataRequest

        macro_agent = MacroDataAgent()

        # Get comprehensive macro data
        macro_request = MacroDataRequest(
            countries=countries,
            indicators=["GDP", "INFLATION", "UNEMPLOYMENT", "INTEREST_RATE", "EXCHANGE_RATE"],
            start_year=2022,
            end_year=2024,
            analysis_type=analysis_type
        )

        response = macro_agent.process(macro_request)

        if response.success:
            # Add business context analysis
            business_analysis = analyze_macro_business_impact(response.data, business_context)

            return {
                "success": True,
                "macro_data": response.data,
                "business_analysis": business_analysis,
                "countries": countries,
                "business_context": business_context,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail=response.error)

    except Exception as e:
        logger.error(f"Macro analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Macro analysis failed: {str(e)}")

def analyze_macro_business_impact(macro_data: Dict[str, Any], business_context: str) -> Dict[str, Any]:
    """Analyze macro economic data for business impact."""
    try:
        analysis = {
            "business_context": business_context,
            "key_insights": [],
            "risk_factors": [],
            "opportunities": [],
            "recommendations": []
        }

        # Analyze based on business context
        if business_context == "telecom":
            analysis["key_insights"].extend([
                "Telecom sector is sensitive to GDP growth and consumer spending",
                "Interest rates affect CAPEX investment decisions",
                "Inflation impacts operational costs and pricing strategies"
            ])

            analysis["risk_factors"].extend([
                "High inflation may pressure margins",
                "Economic slowdown could reduce ARPU",
                "Currency volatility affects international operations"
            ])

            analysis["opportunities"].extend([
                "Digital transformation demand remains strong",
                "5G infrastructure investment opportunities",
                "Cost optimization through automation"
            ])

        elif business_context == "fintech":
            analysis["key_insights"].extend([
                "Fintech growth correlates with digital adoption",
                "Interest rates affect lending and investment products",
                "Regulatory environment varies by country"
            ])

        # Add general recommendations
        analysis["recommendations"].extend([
            "Monitor GDP growth trends for market expansion planning",
            "Hedge currency exposure in multi-country operations",
            "Adjust pricing strategies based on inflation trends",
            "Consider interest rate impact on financing costs"
        ])

        return analysis

    except Exception as e:
        return {"error": f"Business impact analysis failed: {str(e)}"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
