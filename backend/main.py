"""
FastAPI Backend for OneOptimizer.

This is the main FastAPI application that exposes the sophisticated
AI models and analysis algorithms through REST API endpoints.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime

# Import your existing components
from agents.orchestrator_agent import OrchestratorAgent, WorkflowRequest
from services.analysis_service import AnalysisService
from services.forecast_service import ForecastService
from services.wizard_service import WizardService
from models.econometric_models import TimeSeriesAnalyzer, ForecastingEngine
from models.financial_ratios import FinancialRatiosCalculator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="OneOptimizer API",
    description="AI-Powered Budgeting System API with sophisticated analysis algorithms",
    version="2.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Configure CORS for Next.js frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Next.js dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
orchestrator = OrchestratorAgent(verbose=True)
analysis_service = AnalysisService()
forecast_service = ForecastService()
wizard_service = WizardService()

# Algorithm instances for direct access
time_series_analyzer = TimeSeriesAnalyzer()
forecasting_engine = ForecastingEngine()
financial_ratios = FinancialRatiosCalculator()

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "OneOptimizer API",
        "version": "2.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "algorithms": "/api/algorithms",
            "analysis": "/api/analysis",
            "forecast": "/api/forecast",
            "wizard": "/api/wizard",
            "docs": "/api/docs"
        }
    }

@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "orchestrator": "running",
            "analysis_service": "running",
            "forecast_service": "running",
            "wizard_service": "running"
        }
    }

@app.get("/api/algorithms")
async def get_available_algorithms():
    """Get list of available algorithms and their capabilities."""
    try:
        algorithms = {
            "econometric_models": {
                "time_series_analyzer": {
                    "name": "Time Series Analyzer",
                    "description": "ARIMA, seasonal decomposition, trend analysis",
                    "capabilities": ["trend_analysis", "seasonality_detection", "business_insights"],
                    "input_requirements": ["time_series_data"],
                    "output_format": "business_insights_with_recommendations"
                },
                "forecasting_engine": {
                    "name": "Forecasting Engine", 
                    "description": "Auto-ARIMA with cross-validation",
                    "capabilities": ["revenue_forecasting", "confidence_intervals", "scenario_modeling"],
                    "input_requirements": ["historical_data", "forecast_periods"],
                    "output_format": "forecast_with_confidence_bands"
                }
            },
            "financial_analysis": {
                "financial_ratios": {
                    "name": "Financial Ratios Calculator",
                    "description": "Industry-specific ratio analysis for telecom/fintech",
                    "capabilities": ["profitability_analysis", "customer_metrics", "efficiency_ratios"],
                    "input_requirements": ["financial_data"],
                    "output_format": "ratio_analysis_with_benchmarks"
                }
            },
            "ai_agents": {
                "revenue_agent": {
                    "name": "Revenue Agent",
                    "description": "AI-powered revenue forecasting with Bayesian capabilities",
                    "capabilities": ["bayesian_forecasting", "arpu_analysis", "churn_prediction"],
                    "input_requirements": ["revenue_data", "business_context"],
                    "output_format": "comprehensive_revenue_analysis"
                },
                "scenario_agent": {
                    "name": "Scenario Agent",
                    "description": "Monte Carlo simulation and scenario generation",
                    "capabilities": ["monte_carlo_simulation", "risk_assessment", "sensitivity_analysis"],
                    "input_requirements": ["base_assumptions", "risk_parameters"],
                    "output_format": "scenario_analysis_with_probabilities"
                }
            }
        }
        
        return {
            "success": True,
            "algorithms": algorithms,
            "total_count": sum(len(category) for category in algorithms.values()),
            "categories": list(algorithms.keys())
        }
        
    except Exception as e:
        logger.error(f"Error getting algorithms: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get algorithms: {str(e)}")

@app.post("/api/analysis/time-series")
async def analyze_time_series(request: Dict[str, Any]):
    """Run time series analysis on provided data."""
    try:
        data = request.get("data")
        context = request.get("context", "revenue")
        
        if not data:
            raise HTTPException(status_code=400, detail="No data provided")
        
        # Use analysis service for comprehensive analysis
        analysis_config = {
            "time_series_data": {"data": data, "context": context}
        }
        
        result = analysis_service.run_comprehensive_analysis(analysis_config)
        
        return {
            "success": True,
            "analysis": result,
            "algorithm": "time_series_analyzer",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Time series analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/analysis/forecast")
async def create_forecast(request: Dict[str, Any]):
    """Create forecast using econometric or AI methods."""
    try:
        data = request.get("data")
        periods = request.get("periods", 12)
        method = request.get("method", "econometric")  # econometric, ai, or hybrid
        context = request.get("context", "revenue")
        
        if not data:
            raise HTTPException(status_code=400, detail="No data provided")
        
        # Configure forecast based on method
        if method == "econometric":
            forecast_config = {
                "econometric_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        elif method == "ai":
            forecast_config = {
                "ai_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        else:  # hybrid
            forecast_config = {
                "econometric_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                },
                "ai_config": {
                    "data": data,
                    "periods": periods,
                    "context": context
                }
            }
        
        result = forecast_service.create_comprehensive_forecast(forecast_config)
        
        return {
            "success": True,
            "forecast": result,
            "method": method,
            "periods": periods,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Forecast error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Forecast failed: {str(e)}")

@app.post("/api/analysis/financial-ratios")
async def analyze_financial_ratios(request: Dict[str, Any]):
    """Calculate financial ratios and business insights."""
    try:
        financial_data = request.get("financial_data")
        industry_type = request.get("industry_type", "telecom")

        if not financial_data:
            raise HTTPException(status_code=400, detail="No financial data provided")

        # Initialize calculator with industry type
        calculator = FinancialRatiosCalculator(industry_type=industry_type)
        result = calculator.calculate_comprehensive_ratios(financial_data)

        return {
            "success": True,
            "analysis": result,
            "algorithm": "financial_ratios_calculator",
            "industry_type": industry_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Financial ratios analysis error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/agents/revenue")
async def run_revenue_agent(request: Dict[str, Any]):
    """Run revenue agent analysis with AI capabilities."""
    try:
        # Use orchestrator to delegate to revenue agent
        workflow_request = WorkflowRequest(
            task_type="generate_revenue_forecast",
            input_data=request,
            user_message=request.get("user_message", "Generate revenue forecast"),
            session_id=request.get("session_id", "api_session")
        )

        response = orchestrator.process(workflow_request)

        return {
            "success": response.success,
            "data": response.data,
            "message": response.message,
            "agent": "revenue_agent",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Revenue agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Revenue agent failed: {str(e)}")

@app.post("/api/agents/scenario")
async def run_scenario_agent(request: Dict[str, Any]):
    """Run scenario agent for Monte Carlo simulation."""
    try:
        workflow_request = WorkflowRequest(
            task_type="generate_scenarios",
            input_data=request,
            user_message=request.get("user_message", "Generate scenarios"),
            session_id=request.get("session_id", "api_session")
        )

        response = orchestrator.process(workflow_request)

        return {
            "success": response.success,
            "data": response.data,
            "message": response.message,
            "agent": "scenario_agent",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Scenario agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scenario agent failed: {str(e)}")

@app.post("/api/wizard/process")
async def run_wizard_process(request: Dict[str, Any]):
    """Run wizard service for guided analysis."""
    try:
        process_type = request.get("process_type", "comprehensive")

        if process_type == "file_upload":
            files = request.get("files", [])
            result = wizard_service.process_uploaded_files(files)
        elif process_type == "growth_analysis":
            result = wizard_service.analyze_growth_assumptions(request)
        elif process_type == "risk_analysis":
            result = wizard_service.analyze_risk_scenarios(request)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown process type: {process_type}")

        return {
            "success": True,
            "result": result,
            "process_type": process_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Wizard process error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Wizard process failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
